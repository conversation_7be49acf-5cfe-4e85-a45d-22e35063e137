{"mappings": ";;;AAoBA,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,gCAA0B,SAAQ,iBAAiB;IACjD;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;IAEf;;;;OAIG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;;OAGG;IACH,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IAE1C;;;OAGG;IACH,kBAAkB,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;CAC7C;AAED,OAAA,MAAM,kGA+JJ,CAAC;AA2IH,OAAA,MAAM,4FAAiB,CAAC", "sources": ["packages/react/focus-scope/src/packages/react/focus-scope/src/FocusScope.tsx", "packages/react/focus-scope/src/packages/react/focus-scope/src/index.ts", "packages/react/focus-scope/src/index.ts"], "sourcesContent": [null, null, "export {\n  FocusScope,\n  //\n  Root,\n} from './FocusScope';\nexport type { FocusScopeProps } from './FocusScope';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}