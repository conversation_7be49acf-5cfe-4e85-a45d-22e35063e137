import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  X, 
  Heart, 
  Stethoscope,
  Activity,
  FileCheck,
  Download
} from 'lucide-react';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'completed' | 'error';
  progress: number;
}

export function DocumentUpload() {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [dragActive, setDragActive] = useState(false);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFiles = (fileList: File[]) => {
    const newFiles: UploadedFile[] = fileList.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0
    }));
  
    setFiles(prev => [...prev, ...newFiles]);
  
    // 上传每个文件，传递原始 File 对象
    fileList.forEach((file, idx) => {
      uploadFileToServer(newFiles[idx].id, file);
    });
  };

  const uploadFileToServer = async (fileId: string, file: File) => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("http://localhost:8000/upload", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${localStorage.getItem("access_token")}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error("文件上传失败");
      }

      const data = await response.json();
      setFiles(prev => prev.map(f => 
        f.id === fileId ? { ...f, progress: 100, status: 'completed' } : f
      ));
    } catch (error) {
      console.error("上传失败:", error);
      setFiles(prev => prev.map(f => 
        f.id === fileId ? { ...f, status: 'error' } : f
      ));
    }
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-6">
      {/* 医学主题头部 */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
            <Stethoscope className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h1 className="text-3xl font-bold text-primary">医疗文档上传系统</h1>
          <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/20">
            <Heart className="h-8 w-8 text-red-500" />
          </div>
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          安全上传您的医疗文档，支持病历、检查报告、处方单等多种格式。我们将为您的健康数据提供最高级别的安全保护。
        </p>
      </div>

      {/* 上传区域 */}
      <Card className="border-2 border-dashed transition-colors duration-200 hover:border-primary/50">
        <CardContent className="p-8">
          <div
            className={`relative border-2 border-dashed rounded-lg p-8 transition-all duration-200 ${
              dragActive 
                ? 'border-primary bg-primary/5 scale-105' 
                : 'border-muted-foreground/25 hover:border-primary/50'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-4">
                <div className="p-4 rounded-full bg-primary/10">
                  <Upload className="h-12 w-12 text-primary" />
                </div>
                <Activity className="h-8 w-8 text-green-500 animate-pulse" />
              </div>
              
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">拖拽文件到此处上传</h3>
                <p className="text-muted-foreground">
                  或者点击下方按钮选择文件
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 items-center justify-center">
                <Button 
                  onClick={() => document.getElementById('file-input')?.click()}
                  className="min-w-32"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  选择文件
                </Button>
                <span className="text-sm text-muted-foreground">
                  支持 PDF, DOC, DOCX, JPG, PNG 格式
                </span>
              </div>

              <input
                id="file-input"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 文件列表 */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileCheck className="h-5 w-5 text-green-600" />
              已上传文件 ({files.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {files.map((file, index) => (
              <div key={file.id}>
                <div className="flex items-center justify-between gap-4 p-4 rounded-lg bg-muted/30">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex-shrink-0">
                      {file.status === 'completed' ? (
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      ) : file.status === 'error' ? (
                        <AlertCircle className="h-6 w-6 text-red-500" />
                      ) : (
                        <FileText className="h-6 w-6 text-blue-500" />
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{file.name}</p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{formatFileSize(file.size)}</span>
                        <Badge variant="outline" className="text-xs">
                          {file.status === 'completed' ? '已完成' : 
                           file.status === 'error' ? '上传失败' : '上传中'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {file.status === 'completed' && (
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => removeFile(file.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {file.status === 'uploading' && (
                  <div className="mt-2 px-4">
                    <Progress value={file.progress} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-1">
                      上传进度: {Math.round(file.progress)}%
                    </p>
                  </div>
                )}

                {index < files.length - 1 && <Separator className="mt-4" />}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 医学主题的安全提示 */}
      <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-2 rounded-full bg-blue-500/10">
              <Heart className="h-6 w-6 text-blue-600" />
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                您的健康数据安全
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                我们采用医疗级加密技术保护您的文档安全，符合HIPAA和相关医疗数据保护法规。
                所有文件将被安全存储并仅供授权医护人员访问。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}