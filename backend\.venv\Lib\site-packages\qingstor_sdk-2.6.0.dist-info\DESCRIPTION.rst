# QingStor SDK for Python

[![Build Status](https://github.com/qingstor/qingstor-sdk-python/workflows/Unit%20Test/badge.svg?branch=master)](https://github.com/qingstor/qingstor-sdk-python/actions?query=workflow%3A%22Unit+Test%22)
[![API Reference](http://img.shields.io/badge/api-reference-green.svg)](https://docs.qingcloud.com/qingstor/)
[![License](http://img.shields.io/badge/license-apache%20v2-blue.svg)](https://github.com/yunify/qingstor-sdk-python/blob/master/LICENSE)
[![Join the chat](https://img.shields.io/badge/chat-online-blue?style=flat&logo=zulip)](https://qingstor.zulipchat.com/join/nofzrqd5a5skt5ebnaor5b7d/)

English | [中文](README_zh-CN.md)

The official QingStor SDK for the Python programming language.

Before you start using the SDK, make sure you have a basic understanding of the concepts of [QingStor object storage](https://docs.qingcloud.com/qingstor/api/common/overview.html) (such as Zone, Service, Bucket, Object, etc.).

This SDK try to keep a one-to-one correspondence with the methods on the [QingCloud QingStor object storage documentation](https://docs.qingcloud.com/qingstor/api/). For details of each method, please refer to the corresponding chapter in the link.

## Quick Start

Now you are ready to code. You can read the detailed guides in the list below to have a clear understanding.

- [Preparation](./docs/prepare.md)
- [Installation](./docs/install.md)
- [Configuration](./docs/config.md)
- [Service Initialization](./docs/service.md)
- [Code Examples](./docs/examples.md)

Checkout our [releases](https://github.com/yunify/qingstor-sdk-python/releases) and [change log](./CHANGELOG.md) for information about the latest features, bug fixes and new ideas.

## Reference Documentations

- [QingStor Documentation](https://docs.qingcloud.com/qingstor/index.html)
- [QingStor Guide](https://docs.qingcloud.com/qingstor/guide/index.html)
- [QingStor APIs](https://docs.qingcloud.com/qingstor/api/index.html)

## Contributing

Please see [*Contributing Guidelines*](./CONTRIBUTING.md) of this project before submitting patches.

## LICENSE

The Apache License (Version 2.0, January 2004).


