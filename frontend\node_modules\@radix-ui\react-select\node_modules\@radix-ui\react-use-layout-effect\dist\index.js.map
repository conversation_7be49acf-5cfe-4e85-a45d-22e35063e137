{"mappings": ";;;;;;;ACAA;AAEA;;;;;;GAMA,CACA,MAAMA,yCAAe,GAAGE,OAAO,CAACC,UAAD,KAAA,IAAA,IAACA,UAAD,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAACA,UAAU,CAAEC,QAAb,CAAP,GAAgCH,4BAAhC,GAAwD,IAAM,EAAtF,AAAA;;ADTA", "sources": ["packages/react/use-layout-effect/src/index.ts", "packages/react/use-layout-effect/src/useLayoutEffect.tsx"], "sourcesContent": ["export { useLayoutEffect } from './useLayoutEffect';\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "names": ["useLayoutEffect", "React", "Boolean", "globalThis", "document"], "version": 3, "file": "index.js.map"}