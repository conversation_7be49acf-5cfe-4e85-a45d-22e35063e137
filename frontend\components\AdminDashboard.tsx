import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Badge } from './ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from './ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { 
  Brain,
  Search,
  Edit,
  Save,
  X,
  User,
  LogOut,
  Settings,
  FileText,
  Calendar,
  Filter,
  Download,
  Trash2,
  Eye,
  Heart,
  Stethoscope,
  Activity
} from 'lucide-react';

interface DocumentData {
  id: string;
  fileName: string;
  projectName: string;
  applicant: string;
  phone: string;
  story: string;
  keywords: string[];
  extendedSpace: string[];
  knowledgePoints: string[];
  macroAndMicro: string;
  uploadDate: string;
  status: 'completed' | 'processing' | 'error';
}

interface UserData {
  username: string;
  role: string;
}

interface AdminDashboardProps {
  user: UserData;
  onLogout: () => void;
}

// 模拟文档数据
const mockDocuments: DocumentData[] = [
  {
    id: '1',
    fileName: '脂肪肝脏对话研究.pdf',
    projectName: '基于"脂肪-肝脏对话"探讨脂肪组织代谢重编程相关性代谢因子AMRM2调控RNFS/RXRα/PPARα轴在肝脏脂质代谢稳态维持中的作用与机制',
    applicant: '武娇祥',
    phone: '18121223385',
    story: '脂肪细胞——AMRM——肝脏细胞（RNFS/RXRα/PPARα——脂质积聚）',
    keywords: ['靶标——AMRM', '机制——RNFS', '功能——脂质积聚'],
    extendedSpace: ['肥胖、脂肪细胞、代谢物、机制作用方式、机制问题接、脂质积聚、肝细胞、脂肪肝'],
    knowledgePoints: [
      '活性代谢物的概念、研发现状、机制调节模式、生物信息学特征、成药性',
      'AMRM发现、活性证明、调节下游靶标的可能性分析、应用潜能、毒副作用、给药途径'
    ],
    macroAndMicro: '肥胖时肝脏细胞会不会受到机体其他因素影响也会分泌这个代谢物',
    uploadDate: '2025-01-15',
    status: 'completed'
  },
  {
    id: '2',
    fileName: '心血管疾病研究报告.pdf',
    projectName: '心血管疾病发病机制与预防策略研究',
    applicant: '李医生',
    phone: '13900000001',
    story: '血管内皮——炎症因子——动脉硬化（血脂沉积——血管狭窄）',
    keywords: ['血管内皮', '炎症反应', '动脉硬化'],
    extendedSpace: ['高血压、冠心病、血管病变、预防医学、生活方式干预'],
    knowledgePoints: [
      '心血管疾病流行病学特征、危险因素识别与控制',
      '血管内皮功能与动脉硬化机制、早期诊断方法'
    ],
    macroAndMicro: '系统性炎症如何影响局部血管内皮功能',
    uploadDate: '2025-01-14',
    status: 'completed'
  },
  {
    id: '3',
    fileName: '肿瘤免疫治疗方案.pdf',
    projectName: '肿瘤免疫治疗新策略探索',
    applicant: '张研究员',
    phone: '13900000002',
    story: '肿瘤细胞——免疫逃逸——T细胞激活（PD-1/PD-L1——免疫检查点）',
    keywords: ['免疫治疗', 'PD-1抑制剂', '肿瘤微环境'],
    extendedSpace: ['癌症治疗、免疫系统、个体化医疗、靶向治疗、生物标志物'],
    knowledgePoints: [
      '免疫检查点抑制剂作用机制、适应症与禁忌症',
      '肿瘤微环境调节、联合治疗策略、不良反应管理'
    ],
    macroAndMicro: '全身免疫状态如何影响局部肿瘤免疫微环境',
    uploadDate: '2025-01-13',
    status: 'processing'
  }
];

export function AdminDashboard({ user, onLogout }: AdminDashboardProps) {
  const [documents, setDocuments] = useState<DocumentData[]>(mockDocuments);
  const [editingDocument, setEditingDocument] = useState<DocumentData | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.applicant.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.projectName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleEdit = (document: DocumentData) => {
    setEditingDocument({ ...document });
    setIsEditDialogOpen(true);
  };

  const handleSave = () => {
    if (editingDocument) {
      setDocuments(prev => prev.map(doc => 
        doc.id === editingDocument.id ? editingDocument : doc
      ));
      setIsEditDialogOpen(false);
      setEditingDocument(null);
    }
  };

  const handleDelete = (id: string) => {
    if (confirm('确定要删除这个文档吗？')) {
      setDocuments(prev => prev.filter(doc => doc.id !== id));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'processing': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'processing': return '处理中';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-100 dark:bg-blue-900/10 rounded-full opacity-20"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-green-100 dark:bg-green-900/10 rounded-full opacity-20"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-red-100 dark:bg-red-900/10 rounded-full opacity-20"></div>
        <div className="absolute bottom-40 right-1/3 w-16 h-16 bg-purple-100 dark:bg-purple-900/10 rounded-full opacity-20"></div>
      </div>

      <div className="relative z-10 p-4 max-w-7xl mx-auto">
        {/* 顶部导航栏 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/20">
                    <Brain className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h1 className="text-xl font-bold text-primary">医疗文档管理系统</h1>
                    <p className="text-sm text-muted-foreground">后台管理面板</p>
                  </div>
                </div>
                <Activity className="h-5 w-5 text-green-500 animate-pulse" />
              </div>
              
              {/* 用户信息和操作 */}
              <div className="flex items-center gap-4">
                <div className="hidden sm:flex items-center gap-2 text-sm text-muted-foreground">
                  <Stethoscope className="h-4 w-4" />
                  <span>在线用户: {documents.filter(d => d.status === 'completed').length} 个文档已处理</span>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span className="hidden sm:inline">{user.username}</span>
                      {user.role === 'admin' && (
                        <Badge variant="secondary" className="text-xs">管理员</Badge>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      个人设置
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      className="flex items-center gap-2 text-red-600" 
                      onClick={onLogout}
                    >
                      <LogOut className="h-4 w-4" />
                      退出登录
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索文档名称、申请人或项目..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
                >
                  <option value="all">全部状态</option>
                  <option value="completed">已完成</option>
                  <option value="processing">处理中</option>
                  <option value="error">错误</option>
                </select>
              </div>

              <Button variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                导出数据
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/20">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{documents.length}</p>
                  <p className="text-sm text-muted-foreground">总文档数</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/20">
                  <Heart className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{documents.filter(d => d.status === 'completed').length}</p>
                  <p className="text-sm text-muted-foreground">已完成</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                  <Activity className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{documents.filter(d => d.status === 'processing').length}</p>
                  <p className="text-sm text-muted-foreground">处理中</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/20">
                  <Calendar className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold">今日</p>
                  <p className="text-sm text-muted-foreground">3个新文档</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 文档列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              文档分析结果 ({filteredDocuments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>文件名</TableHead>
                    <TableHead>申请人</TableHead>
                    <TableHead>电话</TableHead>
                    <TableHead>上传日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDocuments.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell className="font-medium max-w-48">
                        <div className="truncate" title={doc.fileName}>
                          {doc.fileName}
                        </div>
                      </TableCell>
                      <TableCell>{doc.applicant}</TableCell>
                      <TableCell>{doc.phone}</TableCell>
                      <TableCell>{doc.uploadDate}</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(doc.status)}>
                          {getStatusText(doc.status)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEdit(doc)}
                            className="flex items-center gap-1"
                          >
                            <Edit className="h-3 w-3" />
                            编辑
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="flex items-center gap-1"
                          >
                            <Eye className="h-3 w-3" />
                            查看
                          </Button>
                          {user.role === 'admin' && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleDelete(doc.id)}
                              className="flex items-center gap-1 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                              删除
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* 编辑对话框 */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                编辑文档分析结果
              </DialogTitle>
              <DialogDescription>
                在此编辑文档的分析结果，包括项目信息、关键词、故事内容等各项数据。
              </DialogDescription>
            </DialogHeader>
            
            {editingDocument && (
              <div className="space-y-6 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">申请人</label>
                    <Input
                      value={editingDocument.applicant}
                      onChange={(e) => setEditingDocument({
                        ...editingDocument,
                        applicant: e.target.value
                      })}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">电话</label>
                    <Input
                      value={editingDocument.phone}
                      onChange={(e) => setEditingDocument({
                        ...editingDocument,
                        phone: e.target.value
                      })}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">项目名称</label>
                  <Textarea
                    value={editingDocument.projectName}
                    onChange={(e) => setEditingDocument({
                      ...editingDocument,
                      projectName: e.target.value
                    })}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">故事</label>
                  <Textarea
                    value={editingDocument.story}
                    onChange={(e) => setEditingDocument({
                      ...editingDocument,
                      story: e.target.value
                    })}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">关键词（用逗号分隔）</label>
                  <Input
                    value={editingDocument.keywords.join(', ')}
                    onChange={(e) => setEditingDocument({
                      ...editingDocument,
                      keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">延伸空间</label>
                  <Textarea
                    value={editingDocument.extendedSpace.join('\n')}
                    onChange={(e) => setEditingDocument({
                      ...editingDocument,
                      extendedSpace: e.target.value.split('\n').filter(s => s.trim())
                    })}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">知识点</label>
                  <Textarea
                    value={editingDocument.knowledgePoints.join('\n')}
                    onChange={(e) => setEditingDocument({
                      ...editingDocument,
                      knowledgePoints: e.target.value.split('\n').filter(s => s.trim())
                    })}
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">整体观与微观</label>
                  <Textarea
                    value={editingDocument.macroAndMicro}
                    onChange={(e) => setEditingDocument({
                      ...editingDocument,
                      macroAndMicro: e.target.value
                    })}
                    rows={3}
                  />
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setIsEditDialogOpen(false)}
                  >
                    <X className="h-4 w-4 mr-2" />
                    取消
                  </Button>
                  <Button onClick={handleSave}>
                    <Save className="h-4 w-4 mr-2" />
                    保存
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}