{"mappings": ";;;;A;;;ACKA;;oGAEA,CAEA,MAAMI,0BAAI,GAAG,gBAAb,AAAA;AAMA,MAAMJ,yCAAc,GAAA,aAAGE,CAAAA,iBAAA,CACrB,CAACI,KAAD,EAAQC,YAAR,GAAyB;IACvB,OAAA,aACE,CAAA,oBAAA,CAAC,gBAAD,CAAW,IAAX,EAAA,oCAAA,CAAA,EAAA,EACMD,KADN,EADF;QAGI,GAAG,EAAEC,YAFP;QAGE,KAAK,EAAE;YACL,qFAAA;YACAC,QAAQ,EAAE,UAFL;YAGLC,MAAM,EAAE,CAHH;YAILC,KAAK,EAAE,CAJF;YAKLC,MAAM,EAAE,CALH;YAMLC,OAAO,EAAE,CANJ;YAOLC,MAAM,EAAE,EAPH;YAQLC,QAAQ,EAAE,QARL;YASLC,IAAI,EAAE,kBATD;YAULC,UAAU,EAAE,QAVP;YAWLC,QAAQ,EAAE,QAXL;YAYL,GAAGX,KAAK,CAACY,KAAT;SAZK;KAHT,CAAA,CADF,CACE;CAHiB,CAAvB,AAsBG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,0BAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,MAAMjB,yCAAI,GAAGD,yCAAb,AAAA;;AD5CA", "sources": ["packages/react/visually-hidden/src/index.ts", "packages/react/visually-hidden/src/VisuallyHidden.tsx"], "sourcesContent": ["export {\n  VisuallyHidden,\n  //\n  Root,\n} from './VisuallyHidden';\nexport type { VisuallyHiddenProps } from './VisuallyHidden';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: 'absolute',\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: 'hidden',\n          clip: 'rect(0, 0, 0, 0)',\n          whiteSpace: 'nowrap',\n          wordWrap: 'normal',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n};\nexport type { VisuallyHiddenProps };\n"], "names": ["VisuallyHidden", "Root", "React", "Primitive", "NAME", "forwardRef", "props", "forwardedRef", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "style"], "version": 3, "file": "index.mjs.map"}