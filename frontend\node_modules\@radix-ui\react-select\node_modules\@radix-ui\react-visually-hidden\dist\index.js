var $awrN2$babelruntimehelpersextends = require("@babel/runtime/helpers/extends");
var $awrN2$react = require("react");
var $awrN2$radixuireactprimitive = require("@radix-ui/react-primitive");

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}
function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

$parcel$export(module.exports, "VisuallyHidden", () => $685371e9c20848e2$export$439d29a4e110a164);
$parcel$export(module.exports, "Root", () => $685371e9c20848e2$export$be92b6f5f03c0fe9);



/* -------------------------------------------------------------------------------------------------
 * VisuallyHidden
 * -----------------------------------------------------------------------------------------------*/ const $685371e9c20848e2$var$NAME = 'VisuallyHidden';
const $685371e9c20848e2$export$439d29a4e110a164 = /*#__PURE__*/ $awrN2$react.forwardRef((props, forwardedRef)=>{
    return /*#__PURE__*/ $awrN2$react.createElement($awrN2$radixuireactprimitive.Primitive.span, ($parcel$interopDefault($awrN2$babelruntimehelpersextends))({}, props, {
        ref: forwardedRef,
        style: {
            // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss
            position: 'absolute',
            border: 0,
            width: 1,
            height: 1,
            padding: 0,
            margin: -1,
            overflow: 'hidden',
            clip: 'rect(0, 0, 0, 0)',
            whiteSpace: 'nowrap',
            wordWrap: 'normal',
            ...props.style
        }
    }));
});
/*#__PURE__*/ Object.assign($685371e9c20848e2$export$439d29a4e110a164, {
    displayName: $685371e9c20848e2$var$NAME
});
/* -----------------------------------------------------------------------------------------------*/ const $685371e9c20848e2$export$be92b6f5f03c0fe9 = $685371e9c20848e2$export$439d29a4e110a164;




//# sourceMappingURL=index.js.map
