"use client"
"use client";var pt=Object.create;var le=Object.defineProperty;var gt=Object.getOwnPropertyDescriptor;var bt=Object.getOwnPropertyNames;var vt=Object.getPrototypeOf,ht=Object.prototype.hasOwnProperty;var wt=(e,n)=>{for(var t in n)le(e,t,{get:n[t],enumerable:!0})},Pe=(e,n,t,r)=>{if(n&&typeof n=="object"||typeof n=="function")for(let i of bt(n))!ht.call(e,i)&&i!==t&&le(e,i,{get:()=>n[i],enumerable:!(r=gt(n,i))||r.enumerable});return e};var j=(e,n,t)=>(t=e!=null?pt(vt(e)):{},Pe(n||!e||!e.__esModule?le(t,"default",{value:e,enumerable:!0}):t,e)),Et=e=>Pe(le({},"__esModule",{value:!0}),e);var Nt={};wt(Nt,{Drawer:()=>It});module.exports=Et(Nt);var x=j(require("@radix-ui/react-dialog")),d=j(require("react"));var he=j(require("react")),we=he.default.createContext({drawerRef:{current:null},overlayRef:{current:null},scaleBackground:()=>{},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,isOpen:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},visible:!1,closeDrawer:()=>{},setVisible:()=>{}}),se=()=>he.default.useContext(we);function Ee(e,{insertAt:n}={}){if(!e||typeof document=="undefined")return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",n==="top"&&t.firstChild?t.insertBefore(r,t.firstChild):t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}Ee(`[vaul-drawer]{touch-action:none;transform:translate3d(0,100%,0);transition:transform .5s cubic-bezier(.32,.72,0,1)}.vaul-dragging .vaul-scrollable{overflow-y:hidden!important}[vaul-drawer][vaul-drawer-visible=true]{transform:translate3d(0,var(--snap-point-height, 0),0)}[vaul-overlay]{opacity:0;transition:opacity .5s cubic-bezier(.32,.72,0,1)}[vaul-overlay][vaul-drawer-visible=true]{opacity:1}[vaul-drawer]:after{content:"";position:absolute;top:100%;background:inherit;background-color:inherit;left:0;right:0;height:200%}[vaul-overlay][vaul-snap-points=true]:not([vaul-snap-points-overlay="true"]):not([data-state="closed"]){opacity:0}[vaul-overlay][vaul-snap-points-overlay=true]:not([vaul-drawer-visible="false"]){opacity:1}@keyframes fake-animation{}@media (hover: hover) and (pointer: fine){[vaul-drawer]{user-select:none}}
`);var fe=require("react"),Tt=typeof window!="undefined"?fe.useLayoutEffect:fe.useEffect;function De(...e){return(...n)=>{for(let t of e)typeof t=="function"&&t(...n)}}function yt(){return Re(/^Mac/)}function Dt(){return Re(/^iPhone/)}function St(){return Re(/^iPad/)||yt()&&navigator.maxTouchPoints>1}function Se(){return Dt()||St()}function Re(e){return typeof window!="undefined"&&window.navigator!=null?e.test(window.navigator.platform):void 0}var Te=typeof document!="undefined"&&window.visualViewport;function Fe(e){let n=window.getComputedStyle(e);return/(auto|scroll)/.test(n.overflow+n.overflowX+n.overflowY)}function We(e){for(Fe(e)&&(e=e.parentElement);e&&!Fe(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}var Rt=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),ue=0,ye;function Ue(e={}){let{isDisabled:n}=e;Tt(()=>{if(!n)return ue++,ue===1&&(Se()?ye=Ht():ye=xt()),()=>{ue--,ue===0&&ye()}},[n])}function xt(){return De(ce(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),ce(document.documentElement,"overflow","hidden"))}function Ht(){let e,n=0,t=f=>{e=We(f.target),!(e===document.documentElement&&e===document.body)&&(n=f.changedTouches[0].pageY)},r=f=>{if(!e||e===document.documentElement||e===document.body){f.preventDefault();return}let o=f.changedTouches[0].pageY,C=e.scrollTop,L=e.scrollHeight-e.clientHeight;L!==0&&((C<=0&&o>n||C>=L&&o<n)&&f.preventDefault(),n=o)},i=f=>{let o=f.target;Z(o)&&o!==document.activeElement&&(f.preventDefault(),o.style.transform="translateY(-2000px)",o.focus(),requestAnimationFrame(()=>{o.style.transform=""}))},a=f=>{let o=f.target;Z(o)&&(o.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{o.style.transform="",Te&&(Te.height<window.innerHeight?requestAnimationFrame(()=>{Ve(o)}):Te.addEventListener("resize",()=>Ve(o),{once:!0}))}))},c=()=>{window.scrollTo(0,0)},g=window.pageXOffset,T=window.pageYOffset,w=De(ce(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),ce(document.documentElement,"overflow","hidden"));window.scrollTo(0,0);let S=De(Q(document,"touchstart",t,{passive:!1,capture:!0}),Q(document,"touchmove",r,{passive:!1,capture:!0}),Q(document,"touchend",i,{passive:!1,capture:!0}),Q(document,"focus",a,!0),Q(window,"scroll",c));return()=>{w(),S(),window.scrollTo(g,T)}}function ce(e,n,t){let r=e.style[n];return e.style[n]=t,()=>{e.style[n]=r}}function Q(e,n,t,r){return e.addEventListener(n,t,r),()=>{e.removeEventListener(n,t,r)}}function Ve(e){let n=document.scrollingElement||document.documentElement;for(;e&&e!==n;){let t=We(e);if(t!==document.documentElement&&t!==document.body&&t!==e){let r=t.getBoundingClientRect().top,i=e.getBoundingClientRect().top,a=e.getBoundingClientRect().bottom,c=t.getBoundingClientRect().bottom;a>c&&(t.scrollTop+=i-r)}e=t.parentElement}}function Z(e){return e instanceof HTMLInputElement&&!Rt.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}var je=j(require("react"));function Mt(e,n){typeof e=="function"?e(n):e!=null&&(e.current=n)}function Lt(...e){return n=>e.forEach(t=>Mt(t,n))}function xe(...e){return je.useCallback(Lt(...e),e)}var ee=j(require("react")),F=null;function Ye({isOpen:e,modal:n,nested:t,hasBeenOpened:r}){let[i,a]=ee.default.useState(typeof window!="undefined"?window.location.href:""),c=ee.default.useRef(0);function g(){if(F===null&&e){F={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height};let{scrollX:w,innerHeight:S}=window;document.body.style.setProperty("position","fixed","important"),document.body.style.top=`${-c.current}px`,document.body.style.left=`${-w}px`,document.body.style.right="0px",document.body.style.height="auto",setTimeout(()=>requestAnimationFrame(()=>{let f=S-window.innerHeight;f&&c.current>=S&&(document.body.style.top=`${-(c.current+f)}px`)}),300)}}function T(){if(F!==null){let w=-parseInt(document.body.style.top,10),S=-parseInt(document.body.style.left,10);document.body.style.position=F.position,document.body.style.top=F.top,document.body.style.left=F.left,document.body.style.height=F.height,document.body.style.right="unset",requestAnimationFrame(()=>{if(i!==window.location.href){a(window.location.href);return}window.scrollTo(S,w)}),F=null}}return ee.default.useEffect(()=>{function w(){c.current=window.scrollY}return w(),window.addEventListener("scroll",w),()=>{window.removeEventListener("scroll",w)}},[]),ee.default.useEffect(()=>{t||!r||(e?(g(),n||setTimeout(()=>{T()},500)):T())},[e,r,i]),{restorePositionSetting:T}}var Y=j(require("react"));var ke=new WeakMap;function h(e,n,t=!1){if(!e||!(e instanceof HTMLElement)||!n)return;let r={};Object.entries(n).forEach(([i,a])=>{if(i.startsWith("--")){e.style.setProperty(i,a);return}r[i]=e.style[i],e.style[i]=a}),!t&&ke.set(e,r)}function te(e,n){if(!e||!(e instanceof HTMLElement))return;let t=ke.get(e);t&&(n?e.style[n]=t[n]:Object.entries(t).forEach(([r,i])=>{e.style[r]=i}))}function ne(e){let n=window.getComputedStyle(e),t=n.transform||n.webkitTransform||n.mozTransform,r=t.match(/^matrix3d\((.+)\)$/);return r?parseFloat(r[1].split(", ")[13]):(r=t.match(/^matrix\((.+)\)$/),r?parseFloat(r[1].split(", ")[5]):null)}function ze(e){return 8*(Math.log(e+1)-2)}var v={DURATION:.5,EASE:[.32,.72,0,1]},de=.4;var V=j(require("react"));function Be(e){let n=V.default.useRef(e);return V.default.useEffect(()=>{n.current=e}),V.default.useMemo(()=>(...t)=>{var r;return(r=n.current)==null?void 0:r.call(n,...t)},[])}function Ct({defaultProp:e,onChange:n}){let t=V.default.useState(e),[r]=t,i=V.default.useRef(r),a=Be(n);return V.default.useEffect(()=>{i.current!==r&&(a(r),i.current=r)},[r,i,a]),t}function _e({prop:e,defaultProp:n,onChange:t=()=>{}}){let[r,i]=Ct({defaultProp:n,onChange:t}),a=e!==void 0,c=a?e:r,g=Be(t),T=V.default.useCallback(w=>{if(a){let f=typeof w=="function"?w(e):w;f!==e&&g(f)}else i(w)},[a,e,i,g]);return[c,T]}function qe({activeSnapPointProp:e,setActiveSnapPointProp:n,snapPoints:t,drawerRef:r,overlayRef:i,fadeFromIndex:a,onSnapPointChange:c}){let[g,T]=_e({prop:e,defaultProp:t==null?void 0:t[0],onChange:n}),w=Y.default.useMemo(()=>g===(t==null?void 0:t[t.length-1]),[t,g]),S=t&&t.length>0&&a&&t[a]===g||!t,f=Y.default.useMemo(()=>{var p;return(p=t==null?void 0:t.findIndex(b=>b===g))!=null?p:null},[t,g]),o=Y.default.useMemo(()=>{var p;return(p=t==null?void 0:t.map(b=>{let E=typeof window!="undefined",y=typeof b=="string",O=0;y&&(O=parseInt(b,10));let P=y?O:E?b*window.innerHeight:0;return E?window.innerHeight-P:P}))!=null?p:[]},[t]),C=Y.default.useMemo(()=>f!==null?o==null?void 0:o[f]:null,[o,f]),L=Y.default.useCallback(p=>{var E;let b=(E=o==null?void 0:o.findIndex(y=>y===p))!=null?E:null;c(b),h(r.current,{transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,transform:`translate3d(0, ${p}px, 0)`}),o&&b!==o.length-1&&b!==a?h(i.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,opacity:"0"}):h(i.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,opacity:"1"}),T(b!==null?t==null?void 0:t[b]:null)},[r.current,t,o,a,i,T]);Y.default.useEffect(()=>{var p;if(e){let b=(p=t==null?void 0:t.findIndex(E=>E===e))!=null?p:null;o&&b&&typeof o[b]=="number"&&L(o[b])}},[e,t,o,L]);function N({draggedDistance:p,closeDrawer:b,velocity:E}){if(a===void 0)return;let y=C-p,O=f===a-1,P=f===0;if(O&&h(i.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),E>2&&p<0){b();return}if(E>2&&p>0&&o&&t){L(o[t.length-1]);return}let z=o==null?void 0:o.reduce((A,B)=>typeof A!="number"||typeof B!="number"?A:Math.abs(B-y)<Math.abs(A-y)?B:A);if(E>de&&Math.abs(p)<window.innerHeight*.4){let A=p>0?1:-1;if(A>0&&w){L(o[t.length-1]);return}if(P&&A<0&&b(),f===null)return;L(o[f+A]);return}L(z)}function W({draggedDistance:p}){if(C===null)return;let b=C-p;h(r.current,{transform:`translate3d(0, ${b}px, 0)`})}function H(p,b){if(!t||typeof f!="number"||!o||a===void 0)return null;let E=f===a-1;if(f>=a&&b)return 0;if(E&&!b)return 1;if(!S&&!E)return null;let O=E?f+1:f-1,P=E?o[O]-o[O-1]:o[O+1]-o[O],z=p/Math.abs(P);return E?1-z:z}return{isLastSnapPoint:w,activeSnapPoint:g,shouldFade:S,getPercentageDragged:H,setActiveSnapPoint:T,activeSnapPointIndex:f,onRelease:N,onDrag:W,snapPointsOffset:o}}var Ot=.25,At=100,Ke=8,k=16,Xe=26,Ge="vaul-dragging";function Je({open:e,onOpenChange:n,children:t,shouldScaleBackground:r,onDrag:i,onRelease:a,snapPoints:c,nested:g,closeThreshold:T=Ot,scrollLockTimeout:w=At,dismissible:S=!0,fadeFromIndex:f=c&&c.length-1,activeSnapPoint:o,setActiveSnapPoint:C,fixed:L,modal:N=!0,onClose:W}){var Ne;let[H=!1,p]=d.default.useState(!1),[b,E]=d.default.useState(!1),[y,O]=d.default.useState(!1),[P,z]=d.default.useState(!1),[A,B]=d.default.useState(!1),[et,He]=d.default.useState(!1),U=d.default.useRef(null),re=d.default.useRef(null),me=d.default.useRef(null),Me=d.default.useRef(null),K=d.default.useRef(null),X=d.default.useRef(!1),pe=d.default.useRef(null),ge=d.default.useRef(0),_=d.default.useRef(!1),Le=d.default.useRef(0),u=d.default.useRef(null),Ce=d.default.useRef(((Ne=u.current)==null?void 0:Ne.getBoundingClientRect().height)||0),be=d.default.useRef(0),tt=d.default.useCallback(l=>{c&&l===J.length-1&&(re.current=new Date)},[]),{activeSnapPoint:nt,activeSnapPointIndex:G,setActiveSnapPoint:Oe,onRelease:rt,snapPointsOffset:J,onDrag:ot,shouldFade:Ae,getPercentageDragged:it}=qe({snapPoints:c,activeSnapPointProp:o,setActiveSnapPointProp:C,drawerRef:u,fadeFromIndex:f,overlayRef:U,onSnapPointChange:tt});Ue({isDisabled:!H||A||!N||et||!b});let{restorePositionSetting:at}=Ye({isOpen:H,modal:N,nested:g,hasBeenOpened:b});function oe(){return(window.innerWidth-Xe)/window.innerWidth}function lt(l){var s;!S&&!c||u.current&&!u.current.contains(l.target)||(Ce.current=((s=u.current)==null?void 0:s.getBoundingClientRect().height)||0,B(!0),me.current=new Date,Se()&&window.addEventListener("touchend",()=>X.current=!1,{once:!0}),l.target.setPointerCapture(l.pointerId),ge.current=l.screenY)}function $e(l,s){var $;let m=l,M=new Date,R=($=window.getSelection())==null?void 0:$.toString(),D=u.current?ne(u.current):null;if(re.current&&M.getTime()-re.current.getTime()<500)return!1;if(D>0)return!0;if(R&&R.length>0)return!1;if(K.current&&M.getTime()-K.current.getTime()<w&&D===0)return K.current=new Date,!1;if(s)return K.current=new Date,!1;for(;m;){if(m.scrollHeight>m.clientHeight){if(m.scrollTop!==0)return K.current=new Date,!1;if(m.getAttribute("role")==="dialog")return!0}m=m.parentNode}return!0}function st(l){if(A){let s=ge.current-l.screenY,m=s>0;if(c&&G===0&&!S||!X.current&&!$e(l.target,m))return;if(u.current.classList.add(Ge),X.current=!0,h(u.current,{transition:"none"}),h(U.current,{transition:"none"}),c&&ot({draggedDistance:s}),s>0&&!c){let I=ze(s);h(u.current,{transform:`translate3d(0, ${Math.min(I*-1,0)}px, 0)`});return}let M=Math.abs(s),R=document.querySelector("[vaul-drawer-wrapper]"),D=M/Ce.current,$=it(M,m);$!==null&&(D=$);let ve=1-D;if((Ae||f&&G===f-1)&&(i==null||i(l,D),h(U.current,{opacity:`${ve}`,transition:"none"},!0)),R&&U.current&&r){let I=Math.min(oe()+D*(1-oe()),1),ae=8-D*8,mt=Math.max(0,14-D*14);h(R,{borderRadius:`${ae}px`,transform:`scale(${I}) translate3d(0, ${mt}px, 0)`,transition:"none"},!0)}c||h(u.current,{transform:`translate3d(0, ${M}px, 0)`})}}d.default.useEffect(()=>()=>{ie(!1),at()},[]),d.default.useEffect(()=>{var s;function l(){var M;if(!u.current)return;let m=document.activeElement;if(Z(m)||_.current){let R=((M=window.visualViewport)==null?void 0:M.height)||0,D=window.innerHeight-R,$=u.current.getBoundingClientRect().height||0;be.current||(be.current=$);let ve=u.current.getBoundingClientRect().top;if(Math.abs(Le.current-D)>60&&(_.current=!_.current),c&&c.length>0&&J&&G){let I=J[G]||0;D+=I}if(Le.current=D,$>R||_.current){let I=u.current.getBoundingClientRect().height,ae=I;I>R&&(ae=R-Xe),L?u.current.style.height=`${I-Math.max(D,0)}px`:u.current.style.height=`${Math.max(ae,R-ve)}px`}else u.current.style.height=`${be.current}px`;c&&c.length>0&&!_.current?u.current.style.bottom="0px":u.current.style.bottom=`${Math.max(D,0)}px`}}return(s=window.visualViewport)==null||s.addEventListener("resize",l),()=>{var m;return(m=window.visualViewport)==null?void 0:m.removeEventListener("resize",l)}},[G,c,J]);function q(){u.current&&(W==null||W(),u.current&&(h(u.current,{transform:"translate3d(0, 100%, 0)",transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),h(U.current,{opacity:"0",transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),ie(!1)),setTimeout(()=>{O(!1),p(!1)},300),setTimeout(()=>{c&&Oe(c[0])},500))}d.default.useEffect(()=>{if(!H&&r){let l=setTimeout(()=>{te(document.body)},200);return()=>clearTimeout(l)}},[H,r]),d.default.useEffect(()=>{e?(p(!0),E(!0)):q()},[e]),d.default.useEffect(()=>{P&&(n==null||n(H))},[H]),d.default.useEffect(()=>{z(!0)},[]);function Ie(){if(!u.current)return;let l=document.querySelector("[vaul-drawer-wrapper]"),s=ne(u.current);h(u.current,{transform:"translate3d(0, 0, 0)",transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),h(U.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,opacity:"1"}),r&&s&&s>0&&H&&h(l,{borderRadius:`${Ke}px`,overflow:"hidden",transform:`scale(${oe()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,transformOrigin:"top",transitionProperty:"transform, border-radius",transitionDuration:`${v.DURATION}s`,transitionTimingFunction:`cubic-bezier(${v.EASE.join(",")})`},!0)}function ut(l){if(!A||!u.current)return;X.current&&Z(l.target)&&l.target.blur(),u.current.classList.remove(Ge),X.current=!1,B(!1),Me.current=new Date;let s=ne(u.current);if(!$e(l.target,!1)||!s||Number.isNaN(s)||me.current===null)return;let m=l.screenY,M=Me.current.getTime()-me.current.getTime(),R=ge.current-m,D=Math.abs(R)/M;if(D>.05&&(He(!0),setTimeout(()=>{He(!1)},200)),c){rt({draggedDistance:R,closeDrawer:q,velocity:D});return}if(R>0){Ie(),a==null||a(l,!0);return}if(D>de){q(),a==null||a(l,!1);return}let $=Math.min(u.current.getBoundingClientRect().height||0,window.innerHeight);if(s>=$*T){q(),a==null||a(l,!1);return}a==null||a(l,!0),Ie()}d.default.useEffect(()=>{H&&(re.current=new Date,ie(!0))},[H]),d.default.useEffect(()=>{y&&y&&u.current.querySelectorAll("*").forEach(s=>{let m=s;(m.scrollHeight>m.clientHeight||m.scrollWidth>m.clientWidth)&&m.classList.add("vaul-scrollable")})},[y]);function ie(l){let s=document.querySelector("[vaul-drawer-wrapper]");!s||!r||(l?(h(document.body,{background:"black"},!0),h(s,{borderRadius:`${Ke}px`,overflow:"hidden",transform:`scale(${oe()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,transformOrigin:"top",transitionProperty:"transform, border-radius",transitionDuration:`${v.DURATION}s`,transitionTimingFunction:`cubic-bezier(${v.EASE.join(",")})`})):(te(s,"overflow"),te(s,"transform"),te(s,"borderRadius"),h(s,{transitionProperty:"transform, border-radius",transitionDuration:`${v.DURATION}s`,transitionTimingFunction:`cubic-bezier(${v.EASE.join(",")})`})))}function ct(l){let s=l?(window.innerWidth-k)/window.innerWidth:1,m=l?-k:0;pe.current&&window.clearTimeout(pe.current),h(u.current,{transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,transform:`scale(${s}) translate3d(0, ${m}px, 0)`}),!l&&u.current&&(pe.current=setTimeout(()=>{h(u.current,{transition:"none",transform:`translate3d(0, ${ne(u.current)}px, 0)`})},500))}function ft(l,s){if(s<0)return;let m=(window.innerWidth-k)/window.innerWidth,M=m+s*(1-m),R=-k+s*k;h(u.current,{transform:`scale(${M}) translate3d(0, ${R}px, 0)`,transition:"none"})}function dt(l,s){let m=s?(window.innerWidth-k)/window.innerWidth:1,M=s?-k:0;s&&h(u.current,{transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,transform:`scale(${m}) translate3d(0, ${M}px, 0)`})}return d.default.createElement(x.Root,{modal:N,onOpenChange:l=>{if(e!==void 0){n==null||n(l);return}l?(E(!0),p(l)):q()},open:H},d.default.createElement(we.Provider,{value:{visible:y,activeSnapPoint:nt,snapPoints:c,setActiveSnapPoint:Oe,drawerRef:u,overlayRef:U,scaleBackground:ie,onOpenChange:n,onPress:lt,setVisible:O,onRelease:ut,onDrag:st,dismissible:S,isOpen:H,shouldFade:Ae,closeDrawer:q,onNestedDrag:ft,onNestedOpenChange:ct,onNestedRelease:dt,keyboardIsOpen:_,openProp:e,modal:N,snapPointsOffset:J}},t))}var Qe=d.default.forwardRef(function({children:e,...n},t){let{overlayRef:r,snapPoints:i,onRelease:a,shouldFade:c,isOpen:g,visible:T}=se(),w=xe(t,r),S=i&&i.length>0;return d.default.createElement(x.Overlay,{onMouseUp:a,ref:w,"vaul-drawer-visible":T?"true":"false","vaul-overlay":"","vaul-snap-points":g&&S?"true":"false","vaul-snap-points-overlay":g&&c?"true":"false",...n})});Qe.displayName="Drawer.Overlay";var Ze=d.default.forwardRef(function({children:e,onOpenAutoFocus:n,onPointerDownOutside:t,onAnimationEnd:r,style:i,...a},c){let{drawerRef:g,onPress:T,onRelease:w,onDrag:S,dismissible:f,keyboardIsOpen:o,snapPointsOffset:C,visible:L,closeDrawer:N,modal:W,openProp:H,onOpenChange:p,setVisible:b}=se(),E=xe(c,g);return d.default.useEffect(()=>{b(!0)},[]),d.default.createElement(x.Content,{onOpenAutoFocus:y=>{n?n(y):(y.preventDefault(),g.current.focus())},onPointerDown:T,onPointerDownOutside:y=>{if(t==null||t(y),!W){y.preventDefault();return}o.current&&(o.current=!1),y.preventDefault(),p==null||p(!1),!(!f||H!==void 0)&&N()},onPointerMove:S,onPointerUp:w,ref:E,style:C&&C.length>0?{"--snap-point-height":`${C[0]}px`,...i}:i,...a,"vaul-drawer":"","vaul-drawer-visible":L?"true":"false"},e)});Ze.displayName="Drawer.Content";function $t({children:e,onDrag:n,onOpenChange:t,...r}){let{onNestedDrag:i,onNestedOpenChange:a,onNestedRelease:c}=se();if(!i)throw new Error("Drawer.NestedRoot must be placed in another drawer");return d.default.createElement(Je,{nested:!0,onClose:()=>{a(!1)},onDrag:(g,T)=>{i(g,T),n==null||n(g,T)},onOpenChange:g=>{g&&a(g),t==null||t(g)},onRelease:c,...r},e)}var It={Root:Je,NestedRoot:$t,Content:Ze,Overlay:Qe,Trigger:x.Trigger,Portal:x.Portal,Close:x.Close,Title:x.Title,Description:x.Description};0&&(module.exports={Drawer});
//# sourceMappingURL=index.js.map