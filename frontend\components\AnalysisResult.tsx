import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { 
  FileText, 
  Heart, 
  Stethoscope,
  Activity,
  Brain,
  Search,
  Lightbulb,
  BookOpen,
  Eye,
  Microscope,
  Phone,
  User
} from 'lucide-react';

interface AnalysisData {
  projectName: string;
  applicant: string;
  phone: string;
  story: string;
  keywords: string[];
  extendedSpace: string[];
  knowledgePoints: string[];
  macroAndMicro: string;
}

export function AnalysisResult() {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);

  useEffect(() => {
    const fetchAnalysisData = async () => {
      try {
        const response = await fetch('http://localhost:8000/analysis');
        if (!response.ok) {
          throw new Error('Failed to fetch analysis data');
        }
        const data = await response.json();
        setAnalysisData(data);
      } catch (error) {
        console.error('Error fetching analysis data:', error);
      }
    };

    fetchAnalysisData();
  }, []);

  if (!analysisData) {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full max-w-5xl mx-auto p-4 space-y-6">
      {/* 医学主题头部 */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/20">
            <Brain className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h1 className="text-3xl font-bold text-red-600 dark:text-red-400">关键词提取</h1>
          <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
            <Search className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <p className="text-muted-foreground max-w-3xl mx-auto">
          AI智能分析您的医疗研究文档，提取关键信息和知识要点，为您的研究提供深度洞察。
        </p>
      </div>

      {/* 橙色装饰线 */}
      <div className="w-full h-1 bg-gradient-to-r from-orange-400 via-orange-500 to-orange-400 rounded-full"></div>

      {/* 项目基本信息 */}
      <Card className="border-2 border-gray-300 dark:border-gray-600">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
            <div className="lg:col-span-2">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Badge variant="outline" className="mt-1 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300">
                    项目名称
                  </Badge>
                  <p className="flex-1 leading-relaxed">
                    {analysisData.projectName}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="lg:border-l lg:pl-6 space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">申请人</p>
                  <p className="font-medium">{analysisData.applicant}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">办公电话</p>
                  <p className="font-medium">{analysisData.phone}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 故事部分 */}
      <Card className="border-l-4 border-l-red-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <Heart className="h-5 w-5" />
            故事
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-lg leading-relaxed bg-red-50 dark:bg-red-900/10 p-4 rounded-lg border border-red-200 dark:border-red-800">
            {analysisData.story}
          </p>
        </CardContent>
      </Card>

      {/* 关键词部分 */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
            <Search className="h-5 w-5" />
            关键词
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            {analysisData.keywords.map((keyword, index) => (
              <Badge 
                key={index} 
                variant="secondary" 
                className="px-4 py-2 text-sm bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700"
              >
                {keyword}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 延伸空间部分 */}
      <Card className="border-l-4 border-l-purple-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-purple-600 dark:text-purple-400">
            <Lightbulb className="h-5 w-5" />
            延伸空间
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-purple-50 dark:bg-purple-900/10 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
            {analysisData.extendedSpace.map((item, index) => (
              <p key={index} className="leading-relaxed text-purple-900 dark:text-purple-100">
                {item}
              </p>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 知识点部分 */}
      <Card className="border-l-4 border-l-orange-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-600 dark:text-orange-400">
            <BookOpen className="h-5 w-5" />
            知识点
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analysisData.knowledgePoints.map((point, index) => (
              <div key={index} className="bg-orange-50 dark:bg-orange-900/10 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                <p className="leading-relaxed text-orange-900 dark:text-orange-100">
                  {point}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 整体观与微观部分 */}
      <Card className="border-l-4 border-l-green-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-600 dark:text-green-400">
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              <Microscope className="h-5 w-5" />
            </div>
            整体观与微观
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-lg leading-relaxed bg-green-50 dark:bg-green-900/10 p-4 rounded-lg border border-green-200 dark:border-green-800 text-green-900 dark:text-green-100">
            {analysisData.macroAndMicro}
          </p>
        </CardContent>
      </Card>

      {/* 医学主题的操作提示 */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-blue-500/10">
                <Activity className="h-6 w-6 text-blue-600 animate-pulse" />
              </div>
              <div className="p-2 rounded-full bg-purple-500/10">
                <Stethoscope className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                AI智能分析完成
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                基于先进的医学文献分析算法，我们已为您提取出关键研究要素。
                您可以基于这些结果进行进一步的研究规划和学术探索。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}