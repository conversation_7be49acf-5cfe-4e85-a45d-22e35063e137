{"mappings": ";;;;;;;;ACAA;;AAKA;;;GAGA,CACA,SAASA,yCAAT,CAAiBG,UAAjB,EAAgD;IAC9C,MAAM,CAACC,IAAD,EAAOC,OAAP,CAAA,GAAkBJ,qBAAA,EAAxB,AAAA;IACAA,sBAAA,CAAgB,IAAM;QACpB,IAAIE,UAAJ,EAAgB;YACd,MAAMK,SAAS,GAAGN,qCAAkB,CAACC,UAAD,EAAaE,OAAb,CAApC,AAAA;YACA,OAAO,IAAM;gBACXA,OAAO,CAACI,SAAD,CAAP,CAAAJ;gBACAG,SAAS,EAATA,CAAAA;aAFF,CAGC;SACF;QACD,OAAA;KARF,EASG;QAACL,UAAD;KATH,CASC,CAAA;IACD,OAAOC,IAAP,CAAA;CACD;;ADtBD", "sources": ["packages/react/use-rect/src/index.ts", "packages/react/use-rect/src/useRect.tsx"], "sourcesContent": ["export { useRect } from './useRect';\n", "import * as React from 'react';\nimport { observeElementRect } from '@radix-ui/rect';\n\nimport type { Measurable } from '@radix-ui/rect';\n\n/**\n * Use this custom hook to get access to an element's rect (getBoundingClientRect)\n * and observe it along time.\n */\nfunction useRect(measurable: Measurable | null) {\n  const [rect, setRect] = React.useState<ClientRect>();\n  React.useEffect(() => {\n    if (measurable) {\n      const unobserve = observeElementRect(measurable, setRect);\n      return () => {\n        setRect(undefined);\n        unobserve();\n      };\n    }\n    return;\n  }, [measurable]);\n  return rect;\n}\n\nexport { useRect };\n"], "names": ["useRect", "React", "observeElementRect", "measurable", "rect", "setRect", "useState", "useEffect", "unobserve", "undefined"], "version": 3, "file": "index.js.map"}