{"mappings": "AAAA,yBAAkB;IAAE,qBAAqB,IAAI,UAAU,CAAA;CAAE,CAAC;AAE1D;;;;GAIG;AACH;AACE,wCAAwC;AACxC,gBAAgB,EAAE,UAAU;AAC5B,8DAA8D;AAC9D,QAAQ,EAAE,UAAU,cAwCrB;AAKD,kBAAkB,CAAC,IAAI,EAAE,UAAU,KAAK,IAAI,CAAC", "sources": ["packages/core/rect/src/packages/core/rect/src/observeElementRect.ts", "packages/core/rect/src/packages/core/rect/src/index.ts", "packages/core/rect/src/index.ts"], "sourcesContent": [null, null, "export { observeElementRect } from './observeElementRect';\nexport type { Measurable } from './observeElementRect';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}