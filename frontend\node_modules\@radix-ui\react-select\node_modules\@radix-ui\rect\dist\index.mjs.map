{"mappings": "ACEA;;;;GAIA,CACA,SAASA,yCAAT,CACE,wCAAA,CACAC,gBAFF,EAGE,8DAAA,CACAC,QAJF,EAKE;IACA,MAAMC,aAAY,GAAGC,sCAAgB,CAACC,GAAjB,CAAqBJ,gBAArB,CAArB,AAAA;IAEA,IAAIE,aAAY,KAAKG,SAArB,EAAgC;QAC9B,0EAAA;QACA,0DAAA;QACAF,sCAAgB,CAACG,GAAjB,CAAqBN,gBAArB,EAAuC;YAAEO,IAAI,EAAE,EAAR;YAA0BC,SAAS,EAAE;gBAACP,QAAD;aAAXO;SAAjE,CAAuC,CAAA;QAEvC,IAAIL,sCAAgB,CAACM,IAAjB,KAA0B,CAA9B,EACE,8DAAA;QACAC,2BAAK,GAAGC,qBAAqB,CAACC,6BAAD,CAA7B,CAAAF;KAP<PERSON>,MASO;QACL,gEAAA;QACAR,aAAY,CAACM,SAAb,CAAuBK,IAAvB,CAA4BZ,QAA5B,CAAAC,CAAAA;QACAD,QAAQ,CAACD,gBAAgB,CAACc,qBAAjB,EAAD,CAAR,CAAAb;KACD;IAED,OAAO,IAAM;QACX,MAAMC,YAAY,GAAGC,sCAAgB,CAACC,GAAjB,CAAqBJ,gBAArB,CAArB,AAAA;QACA,IAAIE,YAAY,KAAKG,SAArB,EAAgC,OAFrB,CAIX,iCAFA;QAGA,MAAMU,KAAK,GAAGb,YAAY,CAACM,SAAb,CAAuBQ,OAAvB,CAA+Bf,QAA/B,CAAd,AAAA;QACA,IAAIc,KAAK,GAAG,EAAZ,EACEb,YAAY,CAACM,SAAb,CAAuBS,MAAvB,CAA8BF,KAA9B,EAAqC,CAArC,CAAAb,CAAAA;QAGF,IAAIA,YAAY,CAACM,SAAb,CAAuBU,MAAvB,KAAkC,CAAtC,EAAyC;YACvC,mDAAA;YACA,sCAAA;YACAf,sCAAgB,CAACgB,MAAjB,CAAwBnB,gBAAxB,CAAAG,CAAAA;YAEA,IAAIA,sCAAgB,CAACM,IAAjB,KAA0B,CAA9B,EACE,+DAAA;YACAW,oBAAoB,CAACV,2BAAD,CAApB,CAAAU;SAEH;KAnBH,CAoBC;C,CAGH,2EAFC;AAGD,mBAAA;AASA,IAAIV,2BAAJ,AAAA;AACA,MAAMP,sCAA+C,GAAG,IAAIkB,GAAJ,EAAxD,AAAA;AAEA,SAAST,6BAAT,GAAmB;IACjB,MAAMU,gBAAqC,GAAG,EAA9C,AADiB,EAGjB,sDAFA;IAGAnB,sCAAgB,CAACoB,OAAjB,CAAyB,CAACC,IAAD,EAAOC,OAAP,GAAmB;QAC1C,MAAMC,OAAO,GAAGD,OAAO,CAACX,qBAAR,EAAhB,AAD0C,EAG1C,4DAFA;QAGA,IAAI,CAACa,gCAAU,CAACH,IAAI,CAACjB,IAAN,EAAYmB,OAAZ,CAAf,EAAqC;YACnCF,IAAI,CAACjB,IAAL,GAAYmB,OAAZ,CAAAF;YACAF,gBAAgB,CAACT,IAAjB,CAAsBW,IAAtB,CAAAF,CAAAA;SACD;KAPH,CAAA,CAJiB,CAcjB,oEAFC;IAGD,2DAAA;IACAA,gBAAgB,CAACC,OAAjB,CAA0BC,CAAAA,IAAD,GAAU;QACjCA,IAAI,CAAChB,SAAL,CAAee,OAAf,CAAwBtB,CAAAA,QAAD,GAAcA,QAAQ,CAACuB,IAAI,CAACjB,IAAN,CAA7C;QAAA,CAAAiB,CAAAA;KADF,CAEC,CAAA;IAEDd,2BAAK,GAAGC,qBAAqB,CAACC,6BAAD,CAA7B,CAAAF;C,CAEF,2EADC;AAGD;;GAEA,CACA,SAASiB,gCAAT,CAAoBC,KAApB,EAAuCC,KAAvC,EAA0D;IACxD,OACED,KAAK,CAACE,KAAN,KAAgBD,KAAK,CAACC,KAAtB,IACAF,KAAK,CAACG,MAAN,KAAiBF,KAAK,CAACE,MADvB,IAEAH,KAAK,CAACI,GAAN,KAAcH,KAAK,CAACG,GAFpB,IAGAJ,KAAK,CAACK,KAAN,KAAgBJ,KAAK,CAACI,KAHtB,IAIAL,KAAK,CAACM,MAAN,KAAiBL,KAAK,CAACK,MAJvB,IAKAN,KAAK,CAACO,IAAN,KAAeN,KAAK,CAACM,IANvB,CAAA;CAQD;;ADtGD", "sources": ["packages/core/rect/src/index.ts", "packages/core/rect/src/observeElementRect.ts"], "sourcesContent": ["export { observeElementRect } from './observeElementRect';\nexport type { Measurable } from './observeElementRect';\n", "type Measurable = { getBoundingClientRect(): ClientRect };\n\n/**\n * Observes an element's rectangle on screen (getBoundingClientRect)\n * This is useful to track elements on the screen and attach other elements\n * that might be in different layers, etc.\n */\nfunction observeElementRect(\n  /** The element whose rect to observe */\n  elementToObserve: Measurable,\n  /** The callback which will be called when the rect changes */\n  callback: CallbackFn\n) {\n  const observedData = observedElements.get(elementToObserve);\n\n  if (observedData === undefined) {\n    // add the element to the map of observed elements with its first callback\n    // because this is the first time this element is observed\n    observedElements.set(elementToObserve, { rect: {} as ClientRect, callbacks: [callback] });\n\n    if (observedElements.size === 1) {\n      // start the internal loop once at least 1 element is observed\n      rafId = requestAnimationFrame(runLoop);\n    }\n  } else {\n    // only add a callback for this element as it's already observed\n    observedData.callbacks.push(callback);\n    callback(elementToObserve.getBoundingClientRect());\n  }\n\n  return () => {\n    const observedData = observedElements.get(elementToObserve);\n    if (observedData === undefined) return;\n\n    // start by removing the callback\n    const index = observedData.callbacks.indexOf(callback);\n    if (index > -1) {\n      observedData.callbacks.splice(index, 1);\n    }\n\n    if (observedData.callbacks.length === 0) {\n      // stop observing this element because there are no\n      // callbacks registered for it anymore\n      observedElements.delete(elementToObserve);\n\n      if (observedElements.size === 0) {\n        // stop the internal loop once no elements are observed anymore\n        cancelAnimationFrame(rafId);\n      }\n    }\n  };\n}\n\n// ========================================================================\n// module internals\n\ntype CallbackFn = (rect: ClientRect) => void;\n\ntype ObservedData = {\n  rect: ClientRect;\n  callbacks: Array<CallbackFn>;\n};\n\nlet rafId: number;\nconst observedElements: Map<Measurable, ObservedData> = new Map();\n\nfunction runLoop() {\n  const changedRectsData: Array<ObservedData> = [];\n\n  // process all DOM reads first (getBoundingClientRect)\n  observedElements.forEach((data, element) => {\n    const newRect = element.getBoundingClientRect();\n\n    // gather all the data for elements whose rects have changed\n    if (!rectEquals(data.rect, newRect)) {\n      data.rect = newRect;\n      changedRectsData.push(data);\n    }\n  });\n\n  // group DOM writes here after the DOM reads (getBoundingClientRect)\n  // as DOM writes will most likely happen with the callbacks\n  changedRectsData.forEach((data) => {\n    data.callbacks.forEach((callback) => callback(data.rect));\n  });\n\n  rafId = requestAnimationFrame(runLoop);\n}\n// ========================================================================\n\n/**\n * Returns whether 2 rects are equal in values\n */\nfunction rectEquals(rect1: ClientRect, rect2: ClientRect) {\n  return (\n    rect1.width === rect2.width &&\n    rect1.height === rect2.height &&\n    rect1.top === rect2.top &&\n    rect1.right === rect2.right &&\n    rect1.bottom === rect2.bottom &&\n    rect1.left === rect2.left\n  );\n}\n\nexport { observeElementRect };\nexport type { Measurable };\n"], "names": ["observeElementRect", "elementToObserve", "callback", "observedData", "observedElements", "get", "undefined", "set", "rect", "callbacks", "size", "rafId", "requestAnimationFrame", "run<PERSON><PERSON>", "push", "getBoundingClientRect", "index", "indexOf", "splice", "length", "delete", "cancelAnimationFrame", "Map", "changedRectsData", "for<PERSON>ach", "data", "element", "newRect", "rectEquals", "rect1", "rect2", "width", "height", "top", "right", "bottom", "left"], "version": 3, "file": "index.mjs.map"}