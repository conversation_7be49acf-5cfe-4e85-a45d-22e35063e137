{"mappings": ";;;;;;;;ACAA;;AAGA;;GAEA,CACA,SAASA,yCAAT,CACEG,mBADF,EAEEC,aAAuB,GAAGC,UAAH,KAAA,IAAA,IAAGA,UAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,UAAU,CAAEC,QAFxC,EAGE;IACA,MAAMC,eAAe,GAAGL,gDAAc,CAACC,mBAAD,CAAtC,AAAA;IAEAF,sBAAA,CAAgB,IAAM;QACpB,MAAMQ,aAAa,GAAIC,CAAAA,KAAD,GAA0B;YAC9C,IAAIA,KAAK,CAACC,GAAN,KAAc,QAAlB,EACEJ,eAAe,CAACG,KAAD,CAAf,CAAAH;SAFJ,AAIC;QACDH,aAAa,CAACQ,gBAAd,CAA+B,SAA/B,EAA0CH,aAA1C,CAAAL,CAAAA;QACA,OAAO,IAAMA,aAAa,CAACS,mBAAd,CAAkC,SAAlC,EAA6CJ,aAA7C,CAAb;QAAA,CAAA;KAPF,EAQG;QAACF,eAAD;QAAkBH,aAAlB;KARH,CAQC,CAAA;CACF;;ADrBD", "sources": ["packages/react/use-escape-keydown/src/index.ts", "packages/react/use-escape-keydown/src/useEscapeKeydown.tsx"], "sourcesContent": ["export { useEscapeKeydown } from './useEscapeKeydown';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown);\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown);\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n"], "names": ["useEscapeKeydown", "React", "useCallbackRef", "onEscapeKeyDownProp", "ownerDocument", "globalThis", "document", "onEscapeKeyDown", "useEffect", "handleKeyDown", "event", "key", "addEventListener", "removeEventListener"], "version": 3, "file": "index.js.map"}