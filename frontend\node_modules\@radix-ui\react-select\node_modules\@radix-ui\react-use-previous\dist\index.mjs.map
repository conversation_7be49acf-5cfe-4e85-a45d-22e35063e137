{"mappings": ";;ACAA;AAEA,SAASA,yCAAT,CAAwBE,KAAxB,EAAkC;IAChC,MAAMC,GAAG,GAAGF,aAAA,CAAa;QAH3B,OAG6BC,KAAF;QAASG,QAAQ,EAAEH,KAAVG;KAAtB,CAAZ,AADgC,EAGhC,2DAFyB;IAGzB,6DAAA;IACA,uCAAA;IACA,OAAOJ,cAAA,CAAc,IAAM;QACzB,IAAIE,GAAG,CAACI,OAAJ,CAAYL,KAAZ,KAAsBA,KAA1B,EAAiC;YAC/BC,GAAG,CAACI,OAAJ,CAAYF,QAAZ,GAAuBF,GAAG,CAACI,OAAJ,CAAYL,KAAnC,CAAAC;YACAA,GAAG,CAACI,OAAJ,CAAYL,KAAZ,GAAoBA,KAApB,CAAAC;SACD;QACD,OAAOA,GAAG,CAACI,OAAJ,CAAYF,QAAnB,CAAA;KALK,EAMJ;QAACH,KAAD;KANI,CAAP,CAMC;CACF;;ADfD", "sources": ["packages/react/use-previous/src/index.ts", "packages/react/use-previous/src/usePrevious.tsx"], "sourcesContent": ["export { usePrevious } from './usePrevious';\n", "import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": ["usePrevious", "React", "value", "ref", "useRef", "previous", "useMemo", "current"], "version": 3, "file": "index.mjs.map"}