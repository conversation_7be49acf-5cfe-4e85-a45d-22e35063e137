import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DocumentUpload } from './components/DocumentUpload';
import { AnalysisResult } from './components/AnalysisResult';
import { Login } from './components/Login';
import { AdminDashboard } from './components/AdminDashboard';
import { Button } from './components/ui/button';
import { Card, CardContent } from './components/ui/card';
import { Upload, BarChart3, ArrowLeft, ArrowRight, Settings } from 'lucide-react';

interface UserData {
  username: string;
  role: string;
}

type PageType = 'upload' | 'analysis' | 'admin';

export default function App() {
  const [currentPage, setCurrentPage] = useState<PageType>('upload');
  const [user, setUser] = useState<UserData | null>(null);

  const navigate = useNavigate();

  const handleLogin = (userData: UserData) => {
    setUser(userData);
    if (userData.role === 'admin') {
      navigate('/admin');
    } else {
      navigate('/upload');
    }
  };

  const handleLogout = () => {
    setUser(null);
    setCurrentPage('upload');
  };

  // 如果用户未登录，显示登录页面
  if (!user) {
    return <Login onLogin={handleLogin} />;
  }

  // 如果是管理员且在管理页面，显示管理面板
  if (user.role === 'admin' && currentPage === 'admin') {
    return <AdminDashboard user={user} onLogout={handleLogout} />;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-100 dark:bg-blue-900/10 rounded-full opacity-20"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-green-100 dark:bg-green-900/10 rounded-full opacity-20"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-red-100 dark:bg-red-900/10 rounded-full opacity-20"></div>
        <div className="absolute bottom-40 right-1/3 w-16 h-16 bg-purple-100 dark:bg-purple-900/10 rounded-full opacity-20"></div>
      </div>

      {/* 页面导航 */}
      <div className="relative z-10 pt-6">
        <div className="max-w-5xl mx-auto px-4">
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <Button
                    variant={currentPage === 'upload' ? 'default' : 'outline'}
                    onClick={() => setCurrentPage('upload')}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    文档上传
                  </Button>
                  <Button
                    variant={currentPage === 'analysis' ? 'default' : 'outline'}
                    onClick={() => setCurrentPage('analysis')}
                    className="flex items-center gap-2"
                  >
                    <BarChart3 className="h-4 w-4" />
                    分析结果
                  </Button>
                  {user.role === 'admin' && (
                    <Button
                      variant={currentPage === 'admin' ? 'default' : 'outline'}
                      onClick={() => setCurrentPage('admin')}
                      className="flex items-center gap-2"
                    >
                      <Settings className="h-4 w-4" />
                      管理面板
                    </Button>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  {currentPage === 'analysis' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setCurrentPage('upload')}
                      className="flex items-center gap-1"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      返回上传
                    </Button>
                  )}
                  {currentPage === 'upload' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setCurrentPage('analysis')}
                      className="flex items-center gap-1"
                    >
                      查看分析
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {/* 用户信息和退出按钮 */}
                  <div className="flex items-center gap-2 ml-4 pl-4 border-l border-border">
                    <span className="text-sm text-muted-foreground">
                      欢迎，{user.username}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLogout}
                      className="text-xs"
                    >
                      退出
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10">
        {currentPage === 'upload' && <DocumentUpload />}
        {currentPage === 'analysis' && <AnalysisResult />}
      </div>

      {/* 底部装饰 */}
      <div className="mt-16 border-t border-border/50 bg-muted/20">
        <div className="max-w-5xl mx-auto p-8 text-center">
          <p className="text-sm text-muted-foreground">
            © 2025 医疗文档系统 - 为您的健康数据提供安全可靠的管理服务
          </p>
        </div>
      </div>
    </div>
  );
}