{"mappings": "AAGA;;GAEG;AACH,iCACE,mBAAmB,CAAC,EAAE,CAAC,KAAK,EAAE,aAAa,KAAK,IAAI,EACpD,aAAa,GAAE,QAA+B,QAa/C", "sources": ["packages/react/use-escape-keydown/src/packages/react/use-escape-keydown/src/useEscapeKeydown.tsx", "packages/react/use-escape-keydown/src/packages/react/use-escape-keydown/src/index.ts", "packages/react/use-escape-keydown/src/index.ts"], "sourcesContent": [null, null, "export { useEscapeKeydown } from './useEscapeKeydown';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}