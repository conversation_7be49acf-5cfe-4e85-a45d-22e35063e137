{"classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "Natural Language :: English", "License :: OSI Approved :: Apache Software License", "Programming Language :: Python", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: 3.7", "Programming Language :: Python :: 3.8", "Programming Language :: Python :: 3.9"], "description_content_type": "text/markdown", "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "Yunify SDK Group", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/yunify/qingstor-sdk-python"}}}, "extras": [], "generator": "bdist_wheel (0.29.0)", "license": "Apache License 2.0", "metadata_version": "2.0", "name": "qingstor-sdk", "run_requires": [{"requires": ["PyYAML", "requests"]}], "summary": "The official QingStor SDK for the Python programming language.", "version": "2.6.0"}