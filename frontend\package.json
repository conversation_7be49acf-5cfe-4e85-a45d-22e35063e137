{"name": "medical-document-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.0.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.0.3", "@radix-ui/react-navigation-menu": "^1.1.3", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.0.0", "input-otp": "^1.2.2", "lucide-react": "^0.487.0", "react": "^18.2.0", "react-day-picker": "^8.8.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-router-dom": "^7.8.2", "recharts": "^2.8.0", "sonner": "^1.0.3", "tailwind-merge": "^1.14.0", "vaul": "^0.7.0", "zod": "^3.22.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.2", "vite": "^4.4.5"}}