{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;A;;;;;;;;;;ACyBA,MAAMS,yCAAY,GAAG;IAAC,KAAD;IAAQ,OAAR;IAAiB,QAAjB;IAA2B,MAA3B;CAArB,AAAA;AACA,MAAMC,yCAAa,GAAG;IAAC,OAAD;IAAU,QAAV;IAAoB,KAApB;CAAtB,AAAA;AAKA;;oGAEA,CAEA,MAAMmB,iCAAW,GAAG,QAApB,AAAA;AAGA,MAAM,CAACC,yCAAD,EAAsB9B,uCAAtB,CAAA,GAA2CwB,6CAAkB,CAACK,iCAAD,CAAnE,AAAA;AAMA,MAAM,CAACE,oCAAD,EAAiBC,sCAAjB,CAAA,GAAqCF,yCAAmB,CAAqBD,iCAArB,CAA9D,AAAA;AAKA,MAAM5B,yCAA6B,GAAIgC,CAAAA,KAAD,GAAqC;IACzE,MAAM,E,eAAEC,aAAF,CAAA,E,UAAiBC,QAAAA,CAAAA,EAAjB,GAA8BF,KAApC,AAAM;IACN,MAAM,CAACG,MAAD,EAASC,SAAT,CAAA,GAAsB1B,qBAAA,CAAkC,IAAlC,CAA5B,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,oCAAD,EADF;QACkB,KAAK,EAAEuB,aAAvB;QAAsC,MAAM,EAAEE,MAA9C;QAAsD,cAAc,EAAEC,SAAhB;KAAtD,EACGF,QADH,CADF,CACE;CAJJ,AAQC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMI,iCAAW,GAAG,cAApB,AAAA;AAQA,MAAMrC,wCAAY,GAAA,aAAGS,CAAAA,uBAAA,CACnB,CAACsB,KAAD,EAAwCQ,YAAxC,GAAyD;IACvD,MAAM,E,eAAEP,aAAF,CAAA,E,YAAiBQ,UAAjB,CAAA,EAA6B,GAAGC,WAAH,EAA7B,GAAgDV,KAAtD,AAAM;IACN,MAAMW,OAAO,GAAGZ,sCAAgB,CAACO,iCAAD,EAAcL,aAAd,CAAhC,AAAA;IACA,MAAMW,GAAG,GAAGlC,mBAAA,CAAkC,IAAlC,CAAZ,AAAA;IACA,MAAMoC,YAAY,GAAGxB,8CAAe,CAACkB,YAAD,EAAeI,GAAf,CAApC,AAAA;IAEAlC,sBAAA,CAAgB,IAAM;QACpB,yDAAA;QACA,uDAAA;QACA,mDAAA;QACAiC,OAAO,CAACK,cAAR,CAAuB,AAAAP,CAAAA,UAAU,KAAA,IAAV,IAAAA,UAAU,KAAA,KAAA,CAAV,GAAA,KAAA,CAAA,GAAAA,UAAU,CAAEQ,OAAZ,CAAA,IAAuBL,GAAG,CAACK,OAAlD,CAAAN,CAAAA;KAJF,CAKC,CAAA;IAED,OAAOF,UAAU,GAAG,IAAH,GAAA,aAAU,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAAA,2DAAA,CAAA,EAAA,EAAmBC,WAAnB,EAA3B;QAA2D,GAAG,EAAEI,YAAL;KAAhC,CAAA,CAA3B,CAA2B;CAdV,CAArB,AAeG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMI,kCAAY,GAAG,eAArB,AAAA;AAUA,MAAM,CAACC,2CAAD,EAAwBC,uCAAxB,CAAA,GACJvB,yCAAmB,CAA4BqB,kCAA5B,CADrB,AAAA;AAoBA,MAAMhD,wCAAa,GAAA,aAAGQ,CAAAA,uBAAA,CACpB,CAACsB,KAAD,EAAyCQ,YAAzC,GAA0D;IAAA,IAAA,gBAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,sBAAA,EAAA,oBAAA,AAAA;IACxD,MAAM,E,eACJP,aADI,CAAA,QAEJoB,IAAI,GAAG,QAFH,eAGJC,UAAU,GAAG,CAHT,UAIJC,KAAK,GAAG,QAJJ,gBAKJC,WAAW,GAAG,CALV,iBAMJC,YAAY,GAAG,CANX,sBAOJC,iBAAiB,GAAG,EAPhB,GAQJC,gBAAgB,EAAEC,oBAAoB,GAAG,CARrC,CAAA,UASJC,MAAM,GAAG,SATL,qBAUJC,gBAAgB,GAAG,KAVf,oBAWJC,eAAe,GAAG,IAXd,G,UAYJC,QAZI,CAAA,EAaJ,GAAGC,YAAH,EAbI,GAcFjC,KAdJ,AAAM;IAgBN,MAAMW,OAAO,GAAGZ,sCAAgB,CAACmB,kCAAD,EAAejB,aAAf,CAAhC,AAAA;IAEA,MAAM,CAACiC,OAAD,EAAUC,UAAV,CAAA,GAAwBzD,qBAAA,CAAsC,IAAtC,CAA9B,AAAA;IACA,MAAMoC,YAAY,GAAGxB,8CAAe,CAACkB,YAAD,EAAgB4B,CAAAA,IAAD,GAAUD,UAAU,CAACC,IAAD,CAAnC;IAAA,CAApC,AAAA;IAEA,MAAM,CAACnD,KAAD,EAAQoD,QAAR,CAAA,GAAoB3D,qBAAA,CAAuC,IAAvC,CAA1B,AAAA;IACA,MAAM4D,SAAS,GAAG3C,kCAAO,CAACV,KAAD,CAAzB,AAAA;IACA,MAAMsD,UAAU,GAAA,AAAA,CAAA,gBAAA,GAAGD,SAAH,KAAA,IAAA,IAAGA,SAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,SAAS,CAAEE,KAAd,CAAA,KAAA,IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,GAAA,gBAAA,GAAuB,CAAvC,AAAA;IACA,MAAMC,WAAW,GAAA,AAAA,CAAA,iBAAA,GAAGH,SAAH,KAAA,IAAA,IAAGA,SAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,SAAS,CAAEI,MAAd,CAAA,KAAA,IAAA,IAAA,iBAAA,KAAA,KAAA,CAAA,GAAA,iBAAA,GAAwB,CAAzC,AAAA;IAEA,MAAMC,gBAAgB,GAAItB,IAAI,GAAIE,CAAAA,KAAK,KAAK,QAAV,GAAqB,GAAA,GAAMA,KAA3B,GAAmC,EAAvC,CAAA,AAA9B,AAAA;IAEA,MAAMI,gBAAgB,GACpB,OAAOC,oBAAP,KAAgC,QAAhC,GACIA,oBADJ,GAEI;QAAEgB,GAAG,EAAE,CAAP;QAAUC,KAAK,EAAE,CAAjB;QAAoBC,MAAM,EAAE,CAA5B;QAA+BC,IAAI,EAAE,CAArC;QAAwC,GAAGnB,oBAAH;KAH9C,AAGM;IAEN,MAAMoB,QAAQ,GAAGC,KAAK,CAACC,OAAN,CAAcxB,iBAAd,CAAA,GAAmCA,iBAAnC,GAAuD;QAACA,iBAAD;KAAxE,AAAA;IACA,MAAMyB,qBAAqB,GAAGH,QAAQ,CAACI,MAAT,GAAkB,CAAhD,AAAA;IAEA,MAAMC,qBAAqB,GAAG;QAC5BC,OAAO,EAAE3B,gBADmB;QAE5BqB,QAAQ,EAAEA,QAAQ,CAACO,MAAT,CAAgBC,+BAAhB,CAFkB;QAG5B,iFAAA;QACAC,WAAW,EAAEN,qBAAbM;KAJF,AAA8B;IAO9B,MAAM,E,MAAEC,IAAF,CAAA,E,gBAAQC,cAAR,CAAA,E,WAAwBC,SAAxB,CAAA,E,cAAmCC,YAAnC,CAAA,E,gBAAiDC,cAAAA,CAAAA,EAAjD,GAAoEnF,qCAAW,CAAC;QACpF,gGAAA;QACAoF,QAAQ,EAAE,OAF0E;QAGpFH,SAAS,EAAEjB,gBAHyE;QAIpFqB,oBAAoB,EAAEpF,oCAJ8D;QAKpFqF,QAAQ,EAAE;YACRC,SAAS,EAAEvD,OAAO,CAACR,MAAnB+D;SANkF;QAQpFC,UAAU,EAAE;YACVtF,gCAAM,CAAC;gBAAEuF,QAAQ,EAAE9C,UAAU,GAAGmB,WAAzB;gBAAsC4B,aAAa,EAAE7C,WAAf6C;aAAvC,CADI;YAEVtC,eAAe,IACbjD,+BAAK,CAAC;gBACJsF,QAAQ,EAAE,IADN;gBAEJE,SAAS,EAAE,KAFP;gBAGJC,OAAO,EAAE1C,MAAM,KAAK,SAAX,GAAuB9C,oCAAU,EAAjC,GAAsCyF,SAH3C;gBAIJ,GAAGnB,qBAAH;aAJG,CAHG;YASVtB,eAAe,IAAI5C,8BAAI,CAAC;gBAAE,GAAGkE,qBANrB;aAMe,CATb;YAUVjE,8BAAI,CAAC;gBACH,GAAGiE,qBADA;gBAEHoB,KAAK,EAAE,CAAC,E,UAAER,QAAF,CAAA,E,OAAYS,KAAZ,CAAA,E,gBAAmBC,cAAnB,CAAA,E,iBAAmCC,eAAAA,CAAAA,EAApC,GAA0D;oBAC/D,MAAM,EAAEpC,KAAK,EAAEqC,WAAT,CAAA,EAAsBnC,MAAM,EAAEoC,YAARpC,CAAAA,EAAtB,GAA+CgC,KAAK,CAACR,SAA3D,AAAM;oBACN,MAAMa,YAAY,GAAGd,QAAQ,CAACe,QAAT,CAAkBC,KAAvC,AAAA;oBACAF,YAAY,CAACG,WAAb,CAAyB,gCAAzB,EAA4D,CAAA,EAAEP,cAAe,CAAA,EAAA,CAA7E,CAAAI,CAAAA;oBACAA,YAAY,CAACG,WAAb,CAAyB,iCAAzB,EAA6D,CAAA,EAAEN,eAAgB,CAAA,EAAA,CAA/E,CAAAG,CAAAA;oBACAA,YAAY,CAACG,WAAb,CAAyB,6BAAzB,EAAyD,CAAA,EAAEL,WAAY,CAAA,EAAA,CAAvE,CAAAE,CAAAA;oBACAA,YAAY,CAACG,WAAb,CAAyB,8BAAzB,EAA0D,CAAA,EAAEJ,YAAa,CAAA,EAAA,CAAzE,CAAAC,CAAAA;iBACD;aATC,CAVM;YAqBV9F,KAAK,IAAIC,+BAAe,CAAC;gBAAEiG,OAAO,EAAElG,KAAX;gBAAkBqE,OAAO,EAAE7B,YAAT6B;aAAnB,CArBd;YAsBV8B,qCAAe,CAAC;gB,YAAE7C,UAAF;gB,aAAcE,WAAAA;aAAf,CAtBL;YAuBVX,gBAAgB,IAAI9C,8BAAI,CAAC;gBAAE+E,QAAQ,EAAE,iBAAVA;aAAH,CAvBd;SAuBe;KA/BwD,CAArF,AAAsF;IAmCtF,MAAM,CAACsB,UAAD,EAAaC,WAAb,CAAA,GAA4BC,kDAA4B,CAAC3B,SAAD,CAA9D,AAAA;IAEA,MAAM4B,YAAY,GAAG/F,gDAAc,CAACuC,QAAD,CAAnC,AAAA;IACAtC,kDAAe,CAAC,IAAM;QACpB,IAAImE,YAAJ,EACE2B,YAAY,KAAA,IAAZ,IAAAA,YAAY,KAAA,KAAA,CAAZ,IAAAA,YAAY,EAAZA,CAAAA;KAFW,EAIZ;QAAC3B,YAAD;QAAe2B,YAAf;KAJY,CAAf,CAIC;IAED,MAAMC,MAAM,GAAA,AAAA,CAAA,qBAAA,GAAG3B,cAAc,CAAC7E,KAAlB,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,qBAAA,CAAsByG,CAArC,AAAA;IACA,MAAMC,MAAM,GAAA,AAAA,CAAA,sBAAA,GAAG7B,cAAc,CAAC7E,KAAlB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,sBAAA,CAAsB2G,CAArC,AAAA;IACA,MAAMC,iBAAiB,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA/B,cAAc,CAAC7E,KAAf,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAsB6G,YAAtB,CAAA,KAAuC,CAAjE,AAAA;IAEA,MAAM,CAACC,aAAD,EAAgBC,gBAAhB,CAAA,GAAoCtH,qBAAA,EAA1C,AAAA;IACAgB,kDAAe,CAAC,IAAM;QACpB,IAAIwC,OAAJ,EAAa8D,gBAAgB,CAACC,MAAM,CAACC,gBAAP,CAAwBhE,OAAxB,CAAA,CAAiCiE,MAAlC,CAAhB,CAAb;KADa,EAEZ;QAACjE,OAAD;KAFY,CAAf,CAEC;IAED,OAAA,aACE,CAAA,0BADF,CAAA,KAAA,EAAA;QAEI,GAAG,EAAEwB,IAAI,CAAC0C,WADZ;QAEE,mCAAA,EAAkC,EAFpC;QAGE,KAAK,EAAE;YACL,GAAGzC,cADE;YAEL0C,SAAS,EAAExC,YAAY,GAAGF,cAAc,CAAC0C,SAAlB,GAA8B,qBAFhD;YAEuE,mCAAA;YAC5EC,QAAQ,EAAE,aAHL;YAILH,MAAM,EAAEJ,aAJH;YAKL,CAAC,iCAAD,CAAA,EAA4C;gBAAA,CAAA,qBAAA,GAC1CjC,cAAc,CAACsB,eAD2B,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAC1C,qBAAA,CAAgCM,CADU;gBAAA,CAAA,sBAAA,GAE1C5B,cAAc,CAACsB,eAF2B,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAE1C,sBAAA,CAAgCQ,CAFU;aAAA,CAG1CW,IAH0C,CAGrC,GAHqC,CAA5C;SARJ,CAaE,kFAVO;QAHT;QAgBE,GAAG,EAAEvG,KAAK,CAACwG,GAAX;KAhBF,EAAA,aAkBE,CAAA,0BAAA,CAAC,2CAAD,EAlBF;QAmBI,KAAK,EAAEvG,aADT;QAEE,UAAU,EAAEoF,UAFd;QAGE,aAAa,EAAEhD,QAHjB;QAIE,MAAM,EAAEoD,MAJV;QAKE,MAAM,EAAEE,MALV;QAME,eAAe,EAAEE,iBAAjB;KANF,EAAA,aAQE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EARF,2DAAA,CAAA;QASI,WAAA,EAAWR,UADb;QAEE,YAAA,EAAYC,WAAZ;KAFF,EAGMrD,YAHN,EAAA;QAIE,GAAG,EAAEnB,YAJP;QAKE,KAAK,EAAE;YACL,GAAGmB,YAAY,CAACgD,KADX;YAEL,0EAAA;YACA,gGAAA;YACAwB,SAAS,EAAE,CAAC5C,YAAD,GAAgB,MAAhB,GAAyBW,SAJ/B;YAKL,qEAAA;YACAkC,OAAO,EAAE,AAAA,CAAA,oBAAA,GAAA5C,cAAc,CAAC9E,IAAf,CAAA,KAAA,IAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,IAAA,oBAAA,CAAqB2H,eAArB,GAAuC,CAAvC,GAA2CnC,SAApDkC;SANK;KALT,CAAA,CARF,CAlBF,CADF,CA2BM;CA7HY,CAAtB,AA8IG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAME,gCAAU,GAAG,aAAnB,AAAA;AAEA,MAAMC,mCAAiC,GAAG;IACxCjE,GAAG,EAAE,QADmC;IAExCC,KAAK,EAAE,MAFiC;IAGxCC,MAAM,EAAE,KAHgC;IAIxCC,IAAI,EAAE,OAANA;CAJF,AAA0C;AAW1C,MAAM5E,yCAAW,GAAA,aAAGO,CAAAA,uBAAA,CAAuD,SAASP,yCAAT,CACzE6B,KADyE,EAEzEQ,YAFyE,EAGzE;IACA,MAAM,E,eAAEP,aAAF,CAAA,EAAiB,GAAG6G,UAAH,EAAjB,GAAmC9G,KAAzC,AAAM;IACN,MAAM+G,cAAc,GAAG3F,uCAAiB,CAACwF,gCAAD,EAAa3G,aAAb,CAAxC,AAAA;IACA,MAAM+G,QAAQ,GAAGH,mCAAa,CAACE,cAAc,CAAC1B,UAAhB,CAA9B,AAAA;IAEA,OAAA,aAAA,CACE,+EAAA;IACA,sDAAA;IACA,sFAAA;IACA,0BAAA,CAAA,MAAA,EAAA;QACE,GAAG,EAAE0B,cAAc,CAACE,aADtB;QAEE,KAAK,EAAE;YACLC,QAAQ,EAAE,UADL;YAELnE,IAAI,EAAEgE,cAAc,CAACtB,MAFhB;YAGL7C,GAAG,EAAEmE,cAAc,CAACpB,MAHf;YAIL,CAACqB,QAAD,CAAA,EAAY,CAJP;YAKL5B,eAAe,EAAE;gBACfxC,GAAG,EAAE,EADU;gBAEfC,KAAK,EAAE,KAFQ;gBAGfC,MAAM,EAAE,UAHO;gBAIfC,IAAI,EAAE,QAANA;aAJe,CAKfgE,cAAc,CAAC1B,UALA,CALZ;YAWLgB,SAAS,EAAE;gBACTzD,GAAG,EAAE,kBADI;gBAETC,KAAK,EAAE,gDAFE;gBAGTC,MAAM,EAAG,CAAA,cAAA,CAHA;gBAITC,IAAI,EAAE,gDAANA;aAJS,CAKTgE,cAAc,CAAC1B,UALN,CAXN;YAiBL8B,UAAU,EAAEJ,cAAc,CAACK,eAAf,GAAiC,QAAjC,GAA4C5C,SAAxD2C;SAjBK;KAFT,EAAA,aAsBE,CAAA,0BAAA,CAAC,6BAAD,EAAA,2DAAA,CAAA,EAAA,EACML,UADN,EAtBF;QAwBI,GAAG,EAAEtG,YAFP;QAGE,KAAK,EAAE;YACL,GAAGsG,UAAU,CAAC7B,KADT;YAEL,oEAAA;YACAoC,OAAO,EAAE,OAATA;SAHK;KAHT,CAAA,CAtBF,CAsBE,EA1BJ;CARkB,CAApB,AA6CC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,SAAS7D,+BAAT,CAAsB8D,KAAtB,EAAmD;IACjD,OAAOA,KAAK,KAAK,IAAjB,CAAA;CACD;AAED,MAAMlC,qCAAe,GAAImC,CAAAA,OAAD,GAAuE,CAAA;QAC7FC,IAAI,EAAE,iBADuF;Q,SAE7FD,OAF6F;QAG7FE,EAAE,EAACC,IAAD,EAAO;YAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,AAAA;YACP,MAAM,E,WAAE9D,SAAF,CAAA,E,OAAac,KAAb,CAAA,E,gBAAoBZ,cAAAA,CAAAA,EAApB,GAAuC4D,IAA7C,AAAM;YAEN,MAAM7B,iBAAiB,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA/B,cAAc,CAAC7E,KAAf,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAsB6G,YAAtB,CAAA,KAAuC,CAAjE,AAAA;YACA,MAAM6B,aAAa,GAAG9B,iBAAtB,AAAA;YACA,MAAMtD,UAAU,GAAGoF,aAAa,GAAG,CAAH,GAAOJ,OAAO,CAAChF,UAA/C,AAAA;YACA,MAAME,WAAW,GAAGkF,aAAa,GAAG,CAAH,GAAOJ,OAAO,CAAC9E,WAAhD,AAAA;YAEA,MAAM,CAAC4C,UAAD,EAAaC,WAAb,CAAA,GAA4BC,kDAA4B,CAAC3B,SAAD,CAA9D,AAAA;YACA,MAAMgE,YAAY,GAAG;gBAAEC,KAAK,EAAE,IAAT;gBAAeC,MAAM,EAAE,KAAvB;gBAA8BC,GAAG,EAAE,MAALA;aAA9B,CAA4CzC,WAA5C,CAArB,AAAqB;YAErB,MAAM0C,YAAY,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA,AAAA,CAAA,sBAAA,GAAClE,cAAc,CAAC7E,KAAhB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAC,sBAAA,CAAsByG,CAAvB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,sBAAA,GAA4B,CAA5B,CAAA,GAAiCnD,UAAU,GAAG,CAAnE,AAAA;YACA,MAAM0F,YAAY,GAAG,AAAA,CAAA,AAAA,CAAA,sBAAA,GAAA,AAAA,CAAA,sBAAA,GAACnE,cAAc,CAAC7E,KAAhB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAC,sBAAA,CAAsB2G,CAAvB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,sBAAA,GAA4B,CAA5B,CAAA,GAAiCnD,WAAW,GAAG,CAApE,AAAA;YAEA,IAAIiD,CAAC,GAAG,EAAR,AAAA;YACA,IAAIE,CAAC,GAAG,EAAR,AAAA;YAEA,IAAIP,UAAU,KAAK,QAAnB,EAA6B;gBAC3BK,CAAC,GAAGiC,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEI,YAAa,CAAA,EAAA,CAAnD,CAAAtC;gBACAE,CAAC,GAAI,CAAA,EAAE,CAACnD,WAAY,CAAA,EAAA,CAApB,CAAAmD;aAFF,MAGO,IAAIP,UAAU,KAAK,KAAnB,EAA0B;gBAC/BK,CAAC,GAAGiC,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEI,YAAa,CAAA,EAAA,CAAnD,CAAAtC;gBACAE,CAAC,GAAI,CAAA,EAAElB,KAAK,CAACM,QAAN,CAAetC,MAAf,GAAwBD,WAAY,CAAA,EAAA,CAA3C,CAAAmD;aAFK,MAGA,IAAIP,UAAU,KAAK,OAAnB,EAA4B;gBACjCK,CAAC,GAAI,CAAA,EAAE,CAACjD,WAAY,CAAA,EAAA,CAApB,CAAAiD;gBACAE,CAAC,GAAG+B,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEK,YAAa,CAAA,EAAA,CAAnD,CAAArC;aAFK,MAGA,IAAIP,UAAU,KAAK,MAAnB,EAA2B;gBAChCK,CAAC,GAAI,CAAA,EAAEhB,KAAK,CAACM,QAAN,CAAexC,KAAf,GAAuBC,WAAY,CAAA,EAAA,CAA1C,CAAAiD;gBACAE,CAAC,GAAG+B,aAAa,GAAGC,YAAH,GAAmB,CAAA,EAAEK,YAAa,CAAA,EAAA,CAAnD,CAAArC;aACD;YACD,OAAO;gBAAE8B,IAAI,EAAE;oB,GAAEhC,CAAF;oB,GAAKE,CAAAA;iBAAL;aAAf,CAAO;SACR;KAlCqB,CAAA;AAAuE;AAqC/F,SAASL,kDAAT,CAAsC3B,SAAtC,EAA4D;IAC1D,MAAM,CAACvC,IAAD,EAAOE,KAAK,GAAG,QAAf,CAAA,GAA2BqC,SAAS,CAACsE,KAAV,CAAgB,GAAhB,CAAjC,AAAA;IACA,OAAO;QAAC7G,IAAD;QAAeE,KAAf;KAAP,CAAA;CACD;AAED,MAAMnD,yCAAI,GAAGJ,yCAAb,AAAA;AACA,MAAMK,yCAAM,GAAGJ,wCAAf,AAAA;AACA,MAAMK,yCAAO,GAAGJ,wCAAhB,AAAA;AACA,MAAMK,yCAAK,GAAGJ,yCAAd,AAAA;;ADvYA", "sources": ["packages/react/popper/src/index.ts", "packages/react/popper/src/Popper.tsx"], "sourcesContent": ["export {\n  createPopperScope,\n  //\n  <PERSON><PERSON>,\n  <PERSON>perAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n} from './Popper';\nexport type {\n  PopperProps,\n  PopperAnchorProps,\n  PopperContentProps,\n  PopperArrowProps,\n} from './Popper';\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = typeof SIDE_OPTIONS[number];\ntype Align = typeof ALIGN_OPTIONS[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ElementRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  avoidCollisions?: boolean;\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      avoidCollisions = true,\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: autoUpdate,\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden' }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n              // hide the content if using the hide middleware and should be hidden\n              opacity: middlewareData.hide?.referenceHidden ? 0 : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ElementRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = Radix.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n"], "names": ["createPopperScope", "<PERSON><PERSON>", "PopperA<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PopperArrow", "Root", "<PERSON><PERSON>", "Content", "Arrow", "SIDE_OPTIONS", "ALIGN_OPTIONS", "React", "useFloating", "autoUpdate", "offset", "shift", "limitShift", "hide", "arrow", "floatingUIarrow", "flip", "size", "ArrowPrimitive", "useComposedRefs", "createContextScope", "Primitive", "useCallbackRef", "useLayoutEffect", "useSize", "POPPER_NAME", "createPopperContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "props", "__scope<PERSON>opper", "children", "anchor", "setAnchor", "useState", "ANCHOR_NAME", "forwardRef", "forwardedRef", "virtualRef", "anchorProps", "context", "ref", "useRef", "composedRefs", "useEffect", "onAnchorChange", "current", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "avoidCollisions", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "node", "setArrow", "arrowSize", "arrow<PERSON>idth", "width", "arrowHeight", "height", "desiredPlacement", "top", "right", "bottom", "left", "boundary", "Array", "isArray", "hasExplicitBoundaries", "length", "detectOverflowOptions", "padding", "filter", "isNotNull", "altBoundary", "refs", "floatingStyles", "placement", "isPositioned", "middlewareData", "strategy", "whileElementsMounted", "elements", "reference", "middleware", "mainAxis", "alignmentAxis", "crossAxis", "limiter", "undefined", "apply", "rects", "availableWidth", "availableHeight", "anchorWidth", "anchorHeight", "contentStyle", "floating", "style", "setProperty", "element", "transform<PERSON><PERSON>in", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "arrowX", "x", "arrowY", "y", "cannotCenterArrow", "centerOffset", "contentZIndex", "setContentZIndex", "window", "getComputedStyle", "zIndex", "setFloating", "transform", "min<PERSON><PERSON><PERSON>", "join", "dir", "animation", "opacity", "referenceHidden", "ARROW_NAME", "OPPOSITE_SIDE", "arrowProps", "contentContext", "baseSide", "onArrowChange", "position", "visibility", "shouldHideArrow", "display", "value", "options", "name", "fn", "data", "isArrowHidden", "noArrowAlign", "start", "center", "end", "arrowXCenter", "arrowYCenter", "split"], "version": 3, "file": "index.js.map"}