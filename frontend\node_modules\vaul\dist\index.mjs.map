{"version": 3, "sources": ["../src/index.tsx", "../src/context.ts", "#style-inject:#style-inject", "../src/style.css", "../src/use-prevent-scroll.ts", "../src/use-composed-refs.ts", "../src/use-position-fixed.ts", "../src/use-snap-points.ts", "../src/helpers.ts", "../src/constants.ts", "../src/use-controllable-state.ts"], "sourcesContent": ["'use client';\n\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport React from 'react';\nimport { DrawerContext, useDrawerContext } from './context';\nimport './style.css';\nimport { usePreventScroll, isInput, isIOS } from './use-prevent-scroll';\nimport { useComposedRefs } from './use-composed-refs';\nimport { usePositionFixed } from './use-position-fixed';\nimport { useSnapPoints } from './use-snap-points';\nimport { set, reset, getTranslateY, dampenValue } from './helpers';\nimport { TRANSITIONS, VELOCITY_THRESHOLD } from './constants';\n\nconst CLOSE_THRESHOLD = 0.25;\n\nconst SCROLL_LOCK_TIMEOUT = 100;\n\nconst BORDER_RADIUS = 8;\n\nconst NESTED_DISPLACEMENT = 16;\n\nconst WINDOW_TOP_OFFSET = 26;\n\nconst DRAG_CLASS = 'vaul-dragging';\n\ninterface WithFadeFromProps {\n  snapPoints: (number | string)[];\n  fadeFromIndex: number;\n}\n\ninterface WithoutFadeFromProps {\n  snapPoints?: (number | string)[];\n  fadeFromIndex?: never;\n}\n\ntype DialogProps = {\n  activeSnapPoint?: number | string | null;\n  setActiveSnapPoint?: (snapPoint: number | string | null) => void;\n  children?: React.ReactNode;\n  open?: boolean;\n  closeThreshold?: number;\n  onOpenChange?: (open: boolean) => void;\n  shouldScaleBackground?: boolean;\n  scrollLockTimeout?: number;\n  fixed?: boolean;\n  dismissible?: boolean;\n  onDrag?: (event: React.PointerEvent<HTMLDivElement>, percentageDragged: number) => void;\n  onRelease?: (event: React.PointerEvent<HTMLDivElement>, open: boolean) => void;\n  modal?: boolean;\n  nested?: boolean;\n  onClose?: () => void;\n} & (WithFadeFromProps | WithoutFadeFromProps);\n\nfunction Root({\n  open: openProp,\n  onOpenChange,\n  children,\n  shouldScaleBackground,\n  onDrag: onDragProp,\n  onRelease: onReleaseProp,\n  snapPoints,\n  nested,\n  closeThreshold = CLOSE_THRESHOLD,\n  scrollLockTimeout = SCROLL_LOCK_TIMEOUT,\n  dismissible = true,\n  fadeFromIndex = snapPoints && snapPoints.length - 1,\n  activeSnapPoint: activeSnapPointProp,\n  setActiveSnapPoint: setActiveSnapPointProp,\n  fixed,\n  modal = true,\n  onClose,\n}: DialogProps) {\n  const [isOpen = false, setIsOpen] = React.useState<boolean>(false);\n  const [hasBeenOpened, setHasBeenOpened] = React.useState<boolean>(false);\n  // Not visible = translateY(100%)\n  const [visible, setVisible] = React.useState<boolean>(false);\n  const [mounted, setMounted] = React.useState<boolean>(false);\n  const [isDragging, setIsDragging] = React.useState<boolean>(false);\n  const [justReleased, setJustReleased] = React.useState<boolean>(false);\n  const overlayRef = React.useRef<HTMLDivElement>(null);\n  const openTime = React.useRef<Date | null>(null);\n  const dragStartTime = React.useRef<Date | null>(null);\n  const dragEndTime = React.useRef<Date | null>(null);\n  const lastTimeDragPrevented = React.useRef<Date | null>(null);\n  const isAllowedToDrag = React.useRef<boolean>(false);\n  const nestedOpenChangeTimer = React.useRef<NodeJS.Timeout | null>(null);\n  const pointerStartY = React.useRef(0);\n  const keyboardIsOpen = React.useRef(false);\n  const previousDiffFromInitial = React.useRef(0);\n  const drawerRef = React.useRef<HTMLDivElement>(null);\n  const drawerHeightRef = React.useRef(drawerRef.current?.getBoundingClientRect().height || 0);\n  const initialDrawerHeight = React.useRef(0);\n\n  const onSnapPointChange = React.useCallback((activeSnapPointIndex: number) => {\n    // Change openTime ref when we reach the last snap point to prevent dragging for 500ms incase it's scrollable.\n    if (snapPoints && activeSnapPointIndex === snapPointsOffset.length - 1) openTime.current = new Date();\n  }, []);\n\n  const {\n    activeSnapPoint,\n    activeSnapPointIndex,\n    setActiveSnapPoint,\n    onRelease: onReleaseSnapPoints,\n    snapPointsOffset,\n    onDrag: onDragSnapPoints,\n    shouldFade,\n    getPercentageDragged: getSnapPointsPercentageDragged,\n  } = useSnapPoints({\n    snapPoints,\n    activeSnapPointProp,\n    setActiveSnapPointProp,\n    drawerRef,\n    fadeFromIndex,\n    overlayRef,\n    onSnapPointChange,\n  });\n\n  usePreventScroll({\n    isDisabled: !isOpen || isDragging || !modal || justReleased || !hasBeenOpened,\n  });\n\n  const { restorePositionSetting } = usePositionFixed({\n    isOpen,\n    modal,\n    nested,\n    hasBeenOpened,\n  });\n\n  function getScale() {\n    return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n  }\n\n  function onPress(event: React.PointerEvent<HTMLDivElement>) {\n    if (!dismissible && !snapPoints) return;\n    if (drawerRef.current && !drawerRef.current.contains(event.target as Node)) return;\n    drawerHeightRef.current = drawerRef.current?.getBoundingClientRect().height || 0;\n    setIsDragging(true);\n    dragStartTime.current = new Date();\n\n    // iOS doesn't trigger mouseUp after scrolling so we need to listen to touched in order to disallow dragging\n    if (isIOS()) {\n      window.addEventListener('touchend', () => (isAllowedToDrag.current = false), { once: true });\n    }\n    // Ensure we maintain correct pointer capture even when going outside of the drawer\n    (event.target as HTMLElement).setPointerCapture(event.pointerId);\n\n    pointerStartY.current = event.screenY;\n  }\n\n  function shouldDrag(el: EventTarget, isDraggingDown: boolean) {\n    let element = el as HTMLElement;\n    const date = new Date();\n    const highlightedText = window.getSelection()?.toString();\n    const swipeAmount = drawerRef.current ? getTranslateY(drawerRef.current) : null;\n\n    // Allow scrolling when animating\n    if (openTime.current && date.getTime() - openTime.current.getTime() < 500) {\n      return false;\n    }\n\n    if (swipeAmount > 0) {\n      return true;\n    }\n\n    // Don't drag if there's highlighted text\n    if (highlightedText && highlightedText.length > 0) {\n      return false;\n    }\n\n    // Disallow dragging if drawer was scrolled within `scrollLockTimeout`\n    if (\n      lastTimeDragPrevented.current &&\n      date.getTime() - lastTimeDragPrevented.current.getTime() < scrollLockTimeout &&\n      swipeAmount === 0\n    ) {\n      lastTimeDragPrevented.current = new Date();\n      return false;\n    }\n\n    if (isDraggingDown) {\n      lastTimeDragPrevented.current = new Date();\n\n      // We are dragging down so we should allow scrolling\n      return false;\n    }\n\n    // Keep climbing up the DOM tree as long as there's a parent\n    while (element) {\n      // Check if the element is scrollable\n      if (element.scrollHeight > element.clientHeight) {\n        if (element.scrollTop !== 0) {\n          lastTimeDragPrevented.current = new Date();\n\n          // The element is scrollable and not scrolled to the top, so don't drag\n          return false;\n        }\n\n        if (element.getAttribute('role') === 'dialog') {\n          return true;\n        }\n      }\n\n      // Move up to the parent element\n      element = element.parentNode as HTMLElement;\n    }\n\n    // No scrollable parents not scrolled to the top found, so drag\n    return true;\n  }\n\n  function onDrag(event: React.PointerEvent<HTMLDivElement>) {\n    // We need to know how much of the drawer has been dragged in percentages so that we can transform background accordingly\n    if (isDragging) {\n      const draggedDistance = pointerStartY.current - event.screenY;\n      const isDraggingDown = draggedDistance > 0;\n\n      // Disallow dragging down to close when first snap point is the active one and dismissible prop is set to false.\n      if (snapPoints && activeSnapPointIndex === 0 && !dismissible) return;\n\n      if (!isAllowedToDrag.current && !shouldDrag(event.target, isDraggingDown)) return;\n      drawerRef.current.classList.add(DRAG_CLASS);\n      // If shouldDrag gave true once after pressing down on the drawer, we set isAllowedToDrag to true and it will remain true until we let go, there's no reason to disable dragging mid way, ever, and that's the solution to it\n      isAllowedToDrag.current = true;\n      set(drawerRef.current, {\n        transition: 'none',\n      });\n\n      set(overlayRef.current, {\n        transition: 'none',\n      });\n\n      if (snapPoints) {\n        onDragSnapPoints({ draggedDistance });\n      }\n\n      // Run this only if snapPoints are not defined or if we are at the last snap point (highest one)\n      if (draggedDistance > 0 && !snapPoints) {\n        const dampenedDraggedDistance = dampenValue(draggedDistance);\n\n        set(drawerRef.current, {\n          transform: `translate3d(0, ${Math.min(dampenedDraggedDistance * -1, 0)}px, 0)`,\n        });\n        return;\n      }\n\n      // We need to capture last time when drag with scroll was triggered and have a timeout between\n      const absDraggedDistance = Math.abs(draggedDistance);\n      const wrapper = document.querySelector('[vaul-drawer-wrapper]');\n\n      let percentageDragged = absDraggedDistance / drawerHeightRef.current;\n      const snapPointPercentageDragged = getSnapPointsPercentageDragged(absDraggedDistance, isDraggingDown);\n\n      if (snapPointPercentageDragged !== null) {\n        percentageDragged = snapPointPercentageDragged;\n      }\n\n      const opacityValue = 1 - percentageDragged;\n\n      if (shouldFade || (fadeFromIndex && activeSnapPointIndex === fadeFromIndex - 1)) {\n        onDragProp?.(event, percentageDragged);\n\n        set(\n          overlayRef.current,\n          {\n            opacity: `${opacityValue}`,\n            transition: 'none',\n          },\n          true,\n        );\n      }\n\n      if (wrapper && overlayRef.current && shouldScaleBackground) {\n        // Calculate percentageDragged as a fraction (0 to 1)\n        const scaleValue = Math.min(getScale() + percentageDragged * (1 - getScale()), 1);\n        const borderRadiusValue = 8 - percentageDragged * 8;\n\n        const translateYValue = Math.max(0, 14 - percentageDragged * 14);\n\n        set(\n          wrapper,\n          {\n            borderRadius: `${borderRadiusValue}px`,\n            transform: `scale(${scaleValue}) translate3d(0, ${translateYValue}px, 0)`,\n            transition: 'none',\n          },\n          true,\n        );\n      }\n\n      if (!snapPoints) {\n        set(drawerRef.current, {\n          transform: `translate3d(0, ${absDraggedDistance}px, 0)`,\n        });\n      }\n    }\n  }\n\n  React.useEffect(() => {\n    return () => {\n      scaleBackground(false);\n      restorePositionSetting();\n    };\n  }, []);\n\n  React.useEffect(() => {\n    function onVisualViewportChange() {\n      if (!drawerRef.current) return;\n\n      const focusedElement = document.activeElement as HTMLElement;\n      if (isInput(focusedElement) || keyboardIsOpen.current) {\n        const visualViewportHeight = window.visualViewport?.height || 0;\n        // This is the height of the keyboard\n        let diffFromInitial = window.innerHeight - visualViewportHeight;\n        const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0;\n        if (!initialDrawerHeight.current) {\n          initialDrawerHeight.current = drawerHeight;\n        }\n        const offsetFromTop = drawerRef.current.getBoundingClientRect().top;\n\n        // visualViewport height may change due to some subtle changes to the keyboard. Checking if the height changed by 60 or more will make sure that they keyboard really changed its open state.\n        if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {\n          keyboardIsOpen.current = !keyboardIsOpen.current;\n        }\n\n        if (snapPoints && snapPoints.length > 0 && snapPointsOffset && activeSnapPointIndex) {\n          const activeSnapPointHeight = snapPointsOffset[activeSnapPointIndex] || 0;\n          diffFromInitial += activeSnapPointHeight;\n        }\n\n        previousDiffFromInitial.current = diffFromInitial;\n        // We don't have to change the height if the input is in view, when we are here we are in the opened keyboard state so we can correctly check if the input is in view\n        if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {\n          const height = drawerRef.current.getBoundingClientRect().height;\n          let newDrawerHeight = height;\n\n          if (height > visualViewportHeight) {\n            newDrawerHeight = visualViewportHeight - WINDOW_TOP_OFFSET;\n          }\n          // When fixed, don't move the drawer upwards if there's space, but rather only change it's height so it's fully scrollable when the keyboard is open\n          if (fixed) {\n            drawerRef.current.style.height = `${height - Math.max(diffFromInitial, 0)}px`;\n          } else {\n            drawerRef.current.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`;\n          }\n        } else {\n          drawerRef.current.style.height = `${initialDrawerHeight.current}px`;\n        }\n\n        if (snapPoints && snapPoints.length > 0 && !keyboardIsOpen.current) {\n          drawerRef.current.style.bottom = `0px`;\n        } else {\n          // Negative bottom value would never make sense\n          drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;\n        }\n      }\n    }\n\n    window.visualViewport?.addEventListener('resize', onVisualViewportChange);\n    return () => window.visualViewport?.removeEventListener('resize', onVisualViewportChange);\n  }, [activeSnapPointIndex, snapPoints, snapPointsOffset]);\n\n  function closeDrawer() {\n    if (!drawerRef.current) return;\n\n    onClose?.();\n    if (drawerRef.current) {\n      set(drawerRef.current, {\n        transform: `translate3d(0, 100%, 0)`,\n        transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n      });\n\n      set(overlayRef.current, {\n        opacity: '0',\n        transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n      });\n\n      scaleBackground(false);\n    }\n\n    setTimeout(() => {\n      setVisible(false);\n      setIsOpen(false);\n    }, 300);\n\n    setTimeout(() => {\n      if (snapPoints) {\n        setActiveSnapPoint(snapPoints[0]);\n      }\n    }, 500);\n  }\n\n  React.useEffect(() => {\n    if (!isOpen && shouldScaleBackground) {\n      // Can't use `onAnimationEnd` as the component will be invisible by then\n      const id = setTimeout(() => {\n        reset(document.body);\n      }, 200);\n\n      return () => clearTimeout(id);\n    }\n  }, [isOpen, shouldScaleBackground]);\n\n  // This can be done much better\n  React.useEffect(() => {\n    if (openProp) {\n      setIsOpen(true);\n      setHasBeenOpened(true);\n    } else {\n      closeDrawer();\n    }\n  }, [openProp]);\n\n  // This can be done much better\n  React.useEffect(() => {\n    if (mounted) {\n      onOpenChange?.(isOpen);\n    }\n  }, [isOpen]);\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  function resetDrawer() {\n    if (!drawerRef.current) return;\n    const wrapper = document.querySelector('[vaul-drawer-wrapper]');\n    const currentSwipeAmount = getTranslateY(drawerRef.current);\n\n    set(drawerRef.current, {\n      transform: 'translate3d(0, 0, 0)',\n      transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n    });\n\n    set(overlayRef.current, {\n      transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n      opacity: '1',\n    });\n\n    // Don't reset background if swiped upwards\n    if (shouldScaleBackground && currentSwipeAmount && currentSwipeAmount > 0 && isOpen) {\n      set(\n        wrapper,\n        {\n          borderRadius: `${BORDER_RADIUS}px`,\n          overflow: 'hidden',\n          transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n          transformOrigin: 'top',\n          transitionProperty: 'transform, border-radius',\n          transitionDuration: `${TRANSITIONS.DURATION}s`,\n          transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n        },\n        true,\n      );\n    }\n  }\n\n  function onRelease(event: React.PointerEvent<HTMLDivElement>) {\n    if (!isDragging || !drawerRef.current) return;\n    if (isAllowedToDrag.current && isInput(event.target as HTMLElement)) {\n      // If we were just dragging, prevent focusing on inputs etc. on release\n      (event.target as HTMLInputElement).blur();\n    }\n    drawerRef.current.classList.remove(DRAG_CLASS);\n    isAllowedToDrag.current = false;\n    setIsDragging(false);\n    dragEndTime.current = new Date();\n    const swipeAmount = getTranslateY(drawerRef.current);\n\n    if (!shouldDrag(event.target, false) || !swipeAmount || Number.isNaN(swipeAmount)) return;\n\n    if (dragStartTime.current === null) return;\n\n    const y = event.screenY;\n\n    const timeTaken = dragEndTime.current.getTime() - dragStartTime.current.getTime();\n    const distMoved = pointerStartY.current - y;\n    const velocity = Math.abs(distMoved) / timeTaken;\n\n    if (velocity > 0.05) {\n      // `justReleased` is needed to prevent the drawer from focusing on an input when the drag ends, as it's not the intent most of the time.\n      setJustReleased(true);\n\n      setTimeout(() => {\n        setJustReleased(false);\n      }, 200);\n    }\n\n    if (snapPoints) {\n      onReleaseSnapPoints({\n        draggedDistance: distMoved,\n        closeDrawer,\n        velocity,\n      });\n      return;\n    }\n\n    // Moved upwards, don't do anything\n    if (distMoved > 0) {\n      resetDrawer();\n      onReleaseProp?.(event, true);\n      return;\n    }\n\n    if (velocity > VELOCITY_THRESHOLD) {\n      closeDrawer();\n      onReleaseProp?.(event, false);\n      return;\n    }\n\n    const visibleDrawerHeight = Math.min(drawerRef.current.getBoundingClientRect().height || 0, window.innerHeight);\n\n    if (swipeAmount >= visibleDrawerHeight * closeThreshold) {\n      closeDrawer();\n      onReleaseProp?.(event, false);\n      return;\n    }\n\n    onReleaseProp?.(event, true);\n    resetDrawer();\n  }\n\n  React.useEffect(() => {\n    // Trigger enter animation without using CSS animation\n    if (isOpen) {\n      openTime.current = new Date();\n      scaleBackground(true);\n    }\n  }, [isOpen]);\n\n  React.useEffect(() => {\n    if (visible && visible) {\n      // Find all scrollable elements inside our drawer and assign a class to it so that we can disable overflow when dragging to prevent pointermove not being captured\n      const children = drawerRef.current.querySelectorAll('*');\n      children.forEach((child: Element) => {\n        const htmlChild = child as HTMLElement;\n        if (htmlChild.scrollHeight > htmlChild.clientHeight || htmlChild.scrollWidth > htmlChild.clientWidth) {\n          htmlChild.classList.add('vaul-scrollable');\n        }\n      });\n    }\n  }, [visible]);\n\n  function scaleBackground(open: boolean) {\n    const wrapper = document.querySelector('[vaul-drawer-wrapper]');\n\n    if (!wrapper || !shouldScaleBackground) return;\n\n    if (open) {\n      set(\n        document.body,\n        {\n          background: 'black',\n        },\n        true,\n      );\n\n      set(wrapper, {\n        borderRadius: `${BORDER_RADIUS}px`,\n        overflow: 'hidden',\n        transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n        transformOrigin: 'top',\n        transitionProperty: 'transform, border-radius',\n        transitionDuration: `${TRANSITIONS.DURATION}s`,\n        transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n      });\n    } else {\n      // Exit\n      reset(wrapper, 'overflow');\n      reset(wrapper, 'transform');\n      reset(wrapper, 'borderRadius');\n      set(wrapper, {\n        transitionProperty: 'transform, border-radius',\n        transitionDuration: `${TRANSITIONS.DURATION}s`,\n        transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n      });\n    }\n  }\n\n  function onNestedOpenChange(o: boolean) {\n    const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n    const y = o ? -NESTED_DISPLACEMENT : 0;\n\n    if (nestedOpenChangeTimer.current) {\n      window.clearTimeout(nestedOpenChangeTimer.current);\n    }\n\n    set(drawerRef.current, {\n      transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n      transform: `scale(${scale}) translate3d(0, ${y}px, 0)`,\n    });\n\n    if (!o && drawerRef.current) {\n      nestedOpenChangeTimer.current = setTimeout(() => {\n        set(drawerRef.current, {\n          transition: 'none',\n          transform: `translate3d(0, ${getTranslateY(drawerRef.current as HTMLElement)}px, 0)`,\n        });\n      }, 500);\n    }\n  }\n\n  function onNestedDrag(event: React.PointerEvent<HTMLDivElement>, percentageDragged: number) {\n    if (percentageDragged < 0) return;\n    const initialScale = (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth;\n    const newScale = initialScale + percentageDragged * (1 - initialScale);\n    const newY = -NESTED_DISPLACEMENT + percentageDragged * NESTED_DISPLACEMENT;\n\n    set(drawerRef.current, {\n      transform: `scale(${newScale}) translate3d(0, ${newY}px, 0)`,\n      transition: 'none',\n    });\n  }\n\n  function onNestedRelease(event: React.PointerEvent<HTMLDivElement>, o: boolean) {\n    const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n    const y = o ? -NESTED_DISPLACEMENT : 0;\n\n    if (o) {\n      set(drawerRef.current, {\n        transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n        transform: `scale(${scale}) translate3d(0, ${y}px, 0)`,\n      });\n    }\n  }\n\n  return (\n    <DialogPrimitive.Root\n      modal={modal}\n      onOpenChange={(o: boolean) => {\n        if (openProp !== undefined) {\n          onOpenChange?.(o);\n          return;\n        }\n\n        if (!o) {\n          closeDrawer();\n        } else {\n          setHasBeenOpened(true);\n          setIsOpen(o);\n        }\n      }}\n      open={isOpen}\n    >\n      <DrawerContext.Provider\n        value={{\n          visible,\n          activeSnapPoint,\n          snapPoints,\n          setActiveSnapPoint,\n          drawerRef,\n          overlayRef,\n          scaleBackground,\n          onOpenChange,\n          onPress,\n          setVisible,\n          onRelease,\n          onDrag,\n          dismissible,\n          isOpen,\n          shouldFade,\n          closeDrawer,\n          onNestedDrag,\n          onNestedOpenChange,\n          onNestedRelease,\n          keyboardIsOpen,\n          openProp,\n          modal,\n          snapPointsOffset,\n        }}\n      >\n        {children}\n      </DrawerContext.Provider>\n    </DialogPrimitive.Root>\n  );\n}\n\nconst Overlay = React.forwardRef<HTMLDivElement, React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>>(\n  function ({ children, ...rest }, ref) {\n    const { overlayRef, snapPoints, onRelease, shouldFade, isOpen, visible } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, overlayRef);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n\n    return (\n      <DialogPrimitive.Overlay\n        onMouseUp={onRelease}\n        ref={composedRef}\n        vaul-drawer-visible={visible ? 'true' : 'false'}\n        vaul-overlay=\"\"\n        vaul-snap-points={isOpen && hasSnapPoints ? 'true' : 'false'}\n        vaul-snap-points-overlay={isOpen && shouldFade ? 'true' : 'false'}\n        {...rest}\n      />\n    );\n  },\n);\n\nOverlay.displayName = 'Drawer.Overlay';\n\ntype ContentProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {\n  onAnimationEnd?: (open: boolean) => void;\n};\n\nconst Content = React.forwardRef<HTMLDivElement, ContentProps>(function (\n  { children, onOpenAutoFocus, onPointerDownOutside, onAnimationEnd, style, ...rest },\n  ref,\n) {\n  const {\n    drawerRef,\n    onPress,\n    onRelease,\n    onDrag,\n    dismissible,\n    keyboardIsOpen,\n    snapPointsOffset,\n    visible,\n    closeDrawer,\n    modal,\n    openProp,\n    onOpenChange,\n    setVisible,\n  } = useDrawerContext();\n  const composedRef = useComposedRefs(ref, drawerRef);\n\n  React.useEffect(() => {\n    // Trigger enter animation without using CSS animation\n    setVisible(true);\n  }, []);\n\n  return (\n    <DialogPrimitive.Content\n      onOpenAutoFocus={(e) => {\n        if (onOpenAutoFocus) {\n          onOpenAutoFocus(e);\n        } else {\n          e.preventDefault();\n          drawerRef.current.focus();\n        }\n      }}\n      onPointerDown={onPress}\n      onPointerDownOutside={(e) => {\n        onPointerDownOutside?.(e);\n        if (!modal) {\n          e.preventDefault();\n          return;\n        }\n        if (keyboardIsOpen.current) {\n          keyboardIsOpen.current = false;\n        }\n        e.preventDefault();\n        onOpenChange?.(false);\n        if (!dismissible || openProp !== undefined) {\n          return;\n        }\n\n        closeDrawer();\n      }}\n      onPointerMove={onDrag}\n      onPointerUp={onRelease}\n      ref={composedRef}\n      style={\n        snapPointsOffset && snapPointsOffset.length > 0\n          ? ({\n              '--snap-point-height': `${snapPointsOffset[0]!}px`,\n              ...style,\n            } as React.CSSProperties)\n          : style\n      }\n      {...rest}\n      vaul-drawer=\"\"\n      vaul-drawer-visible={visible ? 'true' : 'false'}\n    >\n      {children}\n    </DialogPrimitive.Content>\n  );\n});\n\nContent.displayName = 'Drawer.Content';\n\nfunction NestedRoot({ children, onDrag, onOpenChange, ...rest }: DialogProps) {\n  const { onNestedDrag, onNestedOpenChange, onNestedRelease } = useDrawerContext();\n\n  if (!onNestedDrag) {\n    throw new Error('Drawer.NestedRoot must be placed in another drawer');\n  }\n\n  return (\n    <Root\n      nested\n      onClose={() => {\n        onNestedOpenChange(false);\n      }}\n      onDrag={(e, p) => {\n        onNestedDrag(e, p);\n        onDrag?.(e, p);\n      }}\n      onOpenChange={(o) => {\n        if (o) {\n          onNestedOpenChange(o);\n        }\n        onOpenChange?.(o);\n      }}\n      onRelease={onNestedRelease}\n      {...rest}\n    >\n      {children}\n    </Root>\n  );\n}\n\nexport const Drawer = {\n  Root,\n  NestedRoot,\n  Content,\n  Overlay,\n  Trigger: DialogPrimitive.Trigger,\n  Portal: DialogPrimitive.Portal,\n  Close: DialogPrimitive.Close,\n  Title: DialogPrimitive.Title,\n  Description: DialogPrimitive.Description,\n};\n", "import React from 'react';\n\ninterface DrawerContextValue {\n  drawerRef: React.RefObject<HTMLDivElement>;\n  overlayRef: React.RefObject<HTMLDivElement>;\n  scaleBackground: (open: boolean) => void;\n  onPress: (event: React.PointerEvent<HTMLDivElement>) => void;\n  onRelease: (event: React.PointerEvent<HTMLDivElement>) => void;\n  onDrag: (event: React.PointerEvent<HTMLDivElement>) => void;\n  onNestedDrag: (event: React.PointerEvent<HTMLDivElement>, percentageDragged: number) => void;\n  onNestedOpenChange: (o: boolean) => void;\n  onNestedRelease: (event: React.PointerEvent<HTMLDivElement>, open: boolean) => void;\n  dismissible: boolean;\n  isOpen: boolean;\n  keyboardIsOpen: React.MutableRefObject<boolean>;\n  snapPointsOffset: number[] | null;\n  snapPoints?: (number | string)[] | null;\n  modal: boolean;\n  shouldFade: boolean;\n  activeSnapPoint?: number | string | null;\n  setActiveSnapPoint: (o: number | string | null) => void;\n  visible: boolean;\n  closeDrawer: () => void;\n  setVisible: (o: boolean) => void;\n  openProp?: boolean;\n  onOpenChange?: (o: boolean) => void;\n}\n\nexport const DrawerContext = React.createContext<DrawerContextValue>({\n  drawerRef: { current: null },\n  overlayRef: { current: null },\n  scaleBackground: () => {},\n  onPress: () => {},\n  onRelease: () => {},\n  onDrag: () => {},\n  onNestedDrag: () => {},\n  onNestedOpenChange: () => {},\n  onNestedRelease: () => {},\n  openProp: undefined,\n  dismissible: false,\n  isOpen: false,\n  keyboardIsOpen: { current: false },\n  snapPointsOffset: null,\n  snapPoints: null,\n  modal: false,\n  shouldFade: false,\n  activeSnapPoint: null,\n  onOpenChange: () => {},\n  setActiveSnapPoint: () => {},\n  visible: false,\n  closeDrawer: () => {},\n  setVisible: () => {},\n});\n\nexport const useDrawerContext = () => React.useContext(DrawerContext);\n", "\n          export default function styleInject(css, { insertAt } = {}) {\n            if (!css || typeof document === 'undefined') return\n          \n            const head = document.head || document.getElementsByTagName('head')[0]\n            const style = document.createElement('style')\n            style.type = 'text/css'\n          \n            if (insertAt === 'top') {\n              if (head.firstChild) {\n                head.insertBefore(style, head.firstChild)\n              } else {\n                head.appendChild(style)\n              }\n            } else {\n              head.appendChild(style)\n            }\n          \n            if (style.styleSheet) {\n              style.styleSheet.cssText = css\n            } else {\n              style.appendChild(document.createTextNode(css))\n            }\n          }\n          ", "import styleInject from '#style-inject';styleInject(\"[vaul-drawer]{touch-action:none;transform:translate3d(0,100%,0);transition:transform .5s cubic-bezier(.32,.72,0,1)}.vaul-dragging .vaul-scrollable{overflow-y:hidden!important}[vaul-drawer][vaul-drawer-visible=true]{transform:translate3d(0,var(--snap-point-height, 0),0)}[vaul-overlay]{opacity:0;transition:opacity .5s cubic-bezier(.32,.72,0,1)}[vaul-overlay][vaul-drawer-visible=true]{opacity:1}[vaul-drawer]:after{content:\\\"\\\";position:absolute;top:100%;background:inherit;background-color:inherit;left:0;right:0;height:200%}[vaul-overlay][vaul-snap-points=true]:not([vaul-snap-points-overlay=\\\"true\\\"]):not([data-state=\\\"closed\\\"]){opacity:0}[vaul-overlay][vaul-snap-points-overlay=true]:not([vaul-drawer-visible=\\\"false\\\"]){opacity:1}@keyframes fake-animation{}@media (hover: hover) and (pointer: fine){[vaul-drawer]{user-select:none}}\\n\")", "// This code comes from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/overlays/src/usePreventScroll.ts\n\nimport { useEffect, useLayoutEffect } from 'react';\n\nexport const useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\n\ninterface PreventScrollOptions {\n  /** Whether the scroll lock is disabled. */\n  isDisabled?: boolean;\n  focusCallback?: () => void;\n}\n\nfunction chain(...callbacks: any[]): (...args: any[]) => void {\n  return (...args: any[]) => {\n    for (let callback of callbacks) {\n      if (typeof callback === 'function') {\n        callback(...args);\n      }\n    }\n  };\n}\n\nfunction isMac(): boolean | undefined {\n  return testPlatform(/^Mac/);\n}\n\nfunction isIPhone(): boolean | undefined {\n  return testPlatform(/^iPhone/);\n}\n\nexport function isSafari(): boolean | undefined {\n  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\n\nfunction isIPad(): boolean | undefined {\n  return (\n    testPlatform(/^iPad/) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1)\n  );\n}\n\nexport function isIOS(): boolean | undefined {\n  return isIPhone() || isIPad();\n}\n\nfunction testPlatform(re: RegExp): boolean | undefined {\n  return typeof window !== 'undefined' && window.navigator != null ? re.test(window.navigator.platform) : undefined;\n}\n\n// @ts-ignore\nconst visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\nexport function isScrollable(node: Element): boolean {\n  let style = window.getComputedStyle(node);\n  return /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n}\n\nexport function getScrollParent(node: Element): Element {\n  if (isScrollable(node)) {\n    node = node.parentElement as HTMLElement;\n  }\n\n  while (node && !isScrollable(node)) {\n    node = node.parentElement as HTMLElement;\n  }\n\n  return node || document.scrollingElement || document.documentElement;\n}\n\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset',\n]);\n\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */\nexport function usePreventScroll(options: PreventScrollOptions = {}) {\n  let { isDisabled } = options;\n\n  useIsomorphicLayoutEffect(() => {\n    if (isDisabled) {\n      return;\n    }\n\n    preventScrollCount++;\n    if (preventScrollCount === 1) {\n      if (isIOS()) {\n        restore = preventScrollMobileSafari();\n      } else {\n        restore = preventScrollStandard();\n      }\n    }\n\n    return () => {\n      preventScrollCount--;\n      if (preventScrollCount === 0) {\n        restore();\n      }\n    };\n  }, [isDisabled]);\n}\n\n// For most browsers, all we need to do is set `overflow: hidden` on the root element, and\n// add some padding to prevent the page from shifting when the scrollbar is hidden.\nfunction preventScrollStandard() {\n  return chain(\n    setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`),\n    setStyle(document.documentElement, 'overflow', 'hidden'),\n  );\n}\n\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Prevent default on `touchmove` events inside a scrollable element when the scroll position is at the\n//    top or bottom. This avoids the whole page scrolling instead, but does prevent overscrolling.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n  let scrollable: Element;\n  let lastY = 0;\n  let onTouchStart = (e: TouchEvent) => {\n    // Store the nearest scrollable parent element from the element that the user touched.\n    scrollable = getScrollParent(e.target as Element);\n    if (scrollable === document.documentElement && scrollable === document.body) {\n      return;\n    }\n\n    lastY = e.changedTouches[0].pageY;\n  };\n\n  let onTouchMove = (e: TouchEvent) => {\n    // Prevent scrolling the window.\n    if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n      e.preventDefault();\n      return;\n    }\n\n    // Prevent scrolling up when at the top and scrolling down when at the bottom\n    // of a nested scrollable area, otherwise mobile Safari will start scrolling\n    // the window instead. Unfortunately, this disables bounce scrolling when at\n    // the top but it's the best we can do.\n    let y = e.changedTouches[0].pageY;\n    let scrollTop = scrollable.scrollTop;\n    let bottom = scrollable.scrollHeight - scrollable.clientHeight;\n\n    if (bottom === 0) {\n      return;\n    }\n\n    if ((scrollTop <= 0 && y > lastY) || (scrollTop >= bottom && y < lastY)) {\n      e.preventDefault();\n    }\n\n    lastY = y;\n  };\n\n  let onTouchEnd = (e: TouchEvent) => {\n    let target = e.target as HTMLElement;\n\n    // Apply this change if we're not already focused on the target element\n    if (isInput(target) && target !== document.activeElement) {\n      e.preventDefault();\n\n      // Apply a transform to trick Safari into thinking the input is at the top of the page\n      // so it doesn't try to scroll it into view. When tapping on an input, this needs to\n      // be done before the \"focus\" event, so we have to focus the element ourselves.\n      target.style.transform = 'translateY(-2000px)';\n      target.focus();\n      requestAnimationFrame(() => {\n        target.style.transform = '';\n      });\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    let target = e.target as HTMLElement;\n    if (isInput(target)) {\n      // Transform also needs to be applied in the focus event in cases where focus moves\n      // other than tapping on an input directly, e.g. the next/previous buttons in the\n      // software keyboard. In these cases, it seems applying the transform in the focus event\n      // is good enough, whereas when tapping an input, it must be done before the focus event. 🤷‍♂️\n      target.style.transform = 'translateY(-2000px)';\n      requestAnimationFrame(() => {\n        target.style.transform = '';\n\n        // This will have prevented the browser from scrolling the focused element into view,\n        // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n        if (visualViewport) {\n          if (visualViewport.height < window.innerHeight) {\n            // If the keyboard is already visible, do this after one additional frame\n            // to wait for the transform to be removed.\n            requestAnimationFrame(() => {\n              scrollIntoView(target);\n            });\n          } else {\n            // Otherwise, wait for the visual viewport to resize before scrolling so we can\n            // measure the correct position to scroll to.\n            visualViewport.addEventListener('resize', () => scrollIntoView(target), { once: true });\n          }\n        }\n      });\n    }\n  };\n\n  let onWindowScroll = () => {\n    // Last resort. If the window scrolled, scroll it back to the top.\n    // It should always be at the top because the body will have a negative margin (see below).\n    window.scrollTo(0, 0);\n  };\n\n  // Record the original scroll position so we can restore it.\n  // Then apply a negative margin to the body to offset it by the scroll position. This will\n  // enable us to scroll the window to the top, which is required for the rest of this to work.\n  let scrollX = window.pageXOffset;\n  let scrollY = window.pageYOffset;\n\n  let restoreStyles = chain(\n    setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`),\n    setStyle(document.documentElement, 'overflow', 'hidden'),\n    // setStyle(document.body, 'marginTop', `-${scrollY}px`),\n  );\n\n  // Scroll to the top. The negative margin on the body will make this appear the same.\n  window.scrollTo(0, 0);\n\n  let removeEvents = chain(\n    addEvent(document, 'touchstart', onTouchStart, { passive: false, capture: true }),\n    addEvent(document, 'touchmove', onTouchMove, { passive: false, capture: true }),\n    addEvent(document, 'touchend', onTouchEnd, { passive: false, capture: true }),\n    addEvent(document, 'focus', onFocus, true),\n    addEvent(window, 'scroll', onWindowScroll),\n  );\n\n  return () => {\n    // Restore styles and scroll the page back to where it was.\n    restoreStyles();\n    removeEvents();\n    window.scrollTo(scrollX, scrollY);\n  };\n}\n\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element: HTMLElement, style: string, value: string) {\n  let cur = element.style[style];\n  element.style[style] = value;\n\n  return () => {\n    element.style[style] = cur;\n  };\n}\n\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent<K extends keyof GlobalEventHandlersEventMap>(\n  target: EventTarget,\n  event: K,\n  handler: (this: Document, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions,\n) {\n  // @ts-ignore\n  target.addEventListener(event, handler, options);\n\n  return () => {\n    // @ts-ignore\n    target.removeEventListener(event, handler, options);\n  };\n}\n\nfunction scrollIntoView(target: Element) {\n  let root = document.scrollingElement || document.documentElement;\n  while (target && target !== root) {\n    // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n    let scrollable = getScrollParent(target);\n    if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== target) {\n      let scrollableTop = scrollable.getBoundingClientRect().top;\n      let targetTop = target.getBoundingClientRect().top;\n      let targetBottom = target.getBoundingClientRect().bottom;\n      const keyboardHeight = scrollable.getBoundingClientRect().bottom;\n\n      if (targetBottom > keyboardHeight) {\n        scrollable.scrollTop += targetTop - scrollableTop;\n      }\n    }\n\n    // @ts-ignore\n    target = scrollable.parentElement;\n  }\n}\n\nexport function isInput(target: Element) {\n  return (\n    (target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type)) ||\n    target instanceof HTMLTextAreaElement ||\n    (target instanceof HTMLElement && target.isContentEditable)\n  );\n}\n", "// This code comes from https://github.com/radix-ui/primitives/tree/main/packages/react/compose-refs\n\nimport * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "import React from 'react';\n\nlet previousBodyPosition: Record<string, string> | null = null;\n\nexport function usePositionFixed({\n  isOpen,\n  modal,\n  nested,\n  hasBeenOpened,\n}: {\n  isOpen: boolean;\n  modal: boolean;\n  nested: boolean;\n  hasBeenOpened: boolean;\n}) {\n  const [activeUrl, setActiveUrl] = React.useState(typeof window !== 'undefined' ? window.location.href : '');\n  const scrollPos = React.useRef(0);\n\n  function setPositionFixed() {\n    // If previousBodyPosition is already set, don't set it again.\n    if (previousBodyPosition === null && isOpen) {\n      previousBodyPosition = {\n        position: document.body.style.position,\n        top: document.body.style.top,\n        left: document.body.style.left,\n        height: document.body.style.height,\n      };\n\n      // Update the dom inside an animation frame\n      const { scrollX, innerHeight } = window;\n\n      document.body.style.setProperty('position', 'fixed', 'important');\n      document.body.style.top = `${-scrollPos.current}px`;\n      document.body.style.left = `${-scrollX}px`;\n      document.body.style.right = '0px';\n      document.body.style.height = 'auto';\n\n      setTimeout(\n        () =>\n          requestAnimationFrame(() => {\n            // Attempt to check if the bottom bar appeared due to the position change\n            const bottomBarHeight = innerHeight - window.innerHeight;\n            if (bottomBarHeight && scrollPos.current >= innerHeight) {\n              // Move the content further up so that the bottom bar doesn't hide it\n              document.body.style.top = `${-(scrollPos.current + bottomBarHeight)}px`;\n            }\n          }),\n        300,\n      );\n    }\n  }\n\n  function restorePositionSetting() {\n    if (previousBodyPosition !== null) {\n      // Convert the position from \"px\" to Int\n      const y = -parseInt(document.body.style.top, 10);\n      const x = -parseInt(document.body.style.left, 10);\n\n      // Restore styles\n      document.body.style.position = previousBodyPosition.position;\n      document.body.style.top = previousBodyPosition.top;\n      document.body.style.left = previousBodyPosition.left;\n      document.body.style.height = previousBodyPosition.height;\n      document.body.style.right = 'unset';\n\n      requestAnimationFrame(() => {\n        if (activeUrl !== window.location.href) {\n          setActiveUrl(window.location.href);\n          return;\n        }\n\n        window.scrollTo(x, y);\n      });\n\n      previousBodyPosition = null;\n    }\n  }\n\n  React.useEffect(() => {\n    function onScroll() {\n      scrollPos.current = window.scrollY;\n    }\n\n    onScroll();\n\n    window.addEventListener('scroll', onScroll);\n\n    return () => {\n      window.removeEventListener('scroll', onScroll);\n    };\n  }, []);\n\n  React.useEffect(() => {\n    if (nested || !hasBeenOpened) return;\n    // This is needed to force Safari toolbar to show **before** the drawer starts animating to prevent a gnarly shift from happening\n    if (isOpen) {\n      setPositionFixed();\n\n      if (!modal) {\n        setTimeout(() => {\n          restorePositionSetting();\n        }, 500);\n      }\n    } else {\n      restorePositionSetting();\n    }\n  }, [isOpen, hasBeenOpened, activeUrl]);\n\n  return { restorePositionSetting };\n}\n", "import React from 'react';\nimport { set } from './helpers';\nimport { TRANSITIONS, VELOCITY_THRESHOLD } from './constants';\nimport { useControllableState } from './use-controllable-state';\n\nexport function useSnapPoints({\n  activeSnapPointProp,\n  setActiveSnapPointProp,\n  snapPoints,\n  drawerRef,\n  overlayRef,\n  fadeFromIndex,\n  onSnapPointChange,\n}: {\n  activeSnapPointProp?: number | string | null;\n  setActiveSnapPointProp?(snapPoint: number | null | string): void;\n  snapPoints?: (number | string)[];\n  fadeFromIndex?: number;\n  drawerRef: React.RefObject<HTMLDivElement>;\n  overlayRef: React.RefObject<HTMLDivElement>;\n  onSnapPointChange(activeSnapPointIndex: number): void;\n}) {\n  const [activeSnapPoint, setActiveSnapPoint] = useControllableState<string | number | null>({\n    prop: activeSnapPointProp,\n    defaultProp: snapPoints?.[0],\n    onChange: setActiveSnapPointProp,\n  });\n\n  const isLastSnapPoint = React.useMemo(\n    () => activeSnapPoint === snapPoints?.[snapPoints.length - 1] ?? null,\n    [snapPoints, activeSnapPoint],\n  );\n\n  const shouldFade =\n    (snapPoints && snapPoints.length > 0 && fadeFromIndex && snapPoints[fadeFromIndex] === activeSnapPoint) ||\n    !snapPoints;\n\n  const activeSnapPointIndex = React.useMemo(\n    () => snapPoints?.findIndex((snapPoint) => snapPoint === activeSnapPoint) ?? null,\n    [snapPoints, activeSnapPoint],\n  );\n\n  const snapPointsOffset = React.useMemo(\n    () =>\n      snapPoints?.map((snapPoint) => {\n        const hasWindow = typeof window !== 'undefined';\n        const isPx = typeof snapPoint === 'string';\n        let snapPointAsNumber = 0;\n\n        if (isPx) {\n          snapPointAsNumber = parseInt(snapPoint, 10);\n        }\n\n        const height = isPx ? snapPointAsNumber : hasWindow ? snapPoint * window.innerHeight : 0;\n\n        if (hasWindow) {\n          return window.innerHeight - height;\n        }\n\n        return height;\n      }) ?? [],\n    [snapPoints],\n  );\n\n  const activeSnapPointOffset = React.useMemo(\n    () => (activeSnapPointIndex !== null ? snapPointsOffset?.[activeSnapPointIndex] : null),\n    [snapPointsOffset, activeSnapPointIndex],\n  );\n\n  const snapToPoint = React.useCallback(\n    (height: number) => {\n      const newSnapPointIndex = snapPointsOffset?.findIndex((snapPointHeight) => snapPointHeight === height) ?? null;\n      onSnapPointChange(newSnapPointIndex);\n      set(drawerRef.current, {\n        transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n        transform: `translate3d(0, ${height}px, 0)`,\n      });\n\n      if (\n        snapPointsOffset &&\n        newSnapPointIndex !== snapPointsOffset.length - 1 &&\n        newSnapPointIndex !== fadeFromIndex\n      ) {\n        set(overlayRef.current, {\n          transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n          opacity: '0',\n        });\n      } else {\n        set(overlayRef.current, {\n          transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n          opacity: '1',\n        });\n      }\n\n      setActiveSnapPoint(newSnapPointIndex !== null ? snapPoints?.[newSnapPointIndex] : null);\n    },\n    [drawerRef.current, snapPoints, snapPointsOffset, fadeFromIndex, overlayRef, setActiveSnapPoint],\n  );\n\n  React.useEffect(() => {\n    if (activeSnapPointProp) {\n      const newIndex = snapPoints?.findIndex((snapPoint) => snapPoint === activeSnapPointProp) ?? null;\n      if (snapPointsOffset && newIndex && typeof snapPointsOffset[newIndex] === 'number') {\n        snapToPoint(snapPointsOffset[newIndex] as number);\n      }\n    }\n  }, [activeSnapPointProp, snapPoints, snapPointsOffset, snapToPoint]);\n\n  function onRelease({\n    draggedDistance,\n    closeDrawer,\n    velocity,\n  }: {\n    draggedDistance: number;\n    closeDrawer: () => void;\n    velocity: number;\n  }) {\n    if (fadeFromIndex === undefined) return;\n\n    const currentPosition = activeSnapPointOffset - draggedDistance;\n    const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n    const isFirst = activeSnapPointIndex === 0;\n\n    if (isOverlaySnapPoint) {\n      set(overlayRef.current, {\n        transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n      });\n    }\n\n    if (velocity > 2 && draggedDistance < 0) {\n      closeDrawer();\n      return;\n    }\n\n    if (velocity > 2 && draggedDistance > 0 && snapPointsOffset && snapPoints) {\n      snapToPoint(snapPointsOffset[snapPoints.length - 1] as number);\n      return;\n    }\n\n    // Find the closest snap point to the current position\n    const closestSnapPoint = snapPointsOffset?.reduce((prev, curr) => {\n      if (typeof prev !== 'number' || typeof curr !== 'number') return prev;\n\n      return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;\n    });\n\n    if (velocity > VELOCITY_THRESHOLD && Math.abs(draggedDistance) < window.innerHeight * 0.4) {\n      // -1 = down, 1 = up, might need a better name\n      const dragDirection = draggedDistance > 0 ? 1 : -1;\n\n      // Don't do anything if we swipe upwards while being on the last snap point\n      if (dragDirection > 0 && isLastSnapPoint) {\n        snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n        return;\n      }\n\n      if (isFirst && dragDirection < 0) {\n        closeDrawer();\n      }\n\n      if (activeSnapPointIndex === null) return;\n\n      snapToPoint(snapPointsOffset[activeSnapPointIndex + dragDirection]);\n      return;\n    }\n\n    snapToPoint(closestSnapPoint);\n  }\n\n  function onDrag({ draggedDistance }: { draggedDistance: number }) {\n    if (activeSnapPointOffset === null) return;\n    const newYValue = activeSnapPointOffset - draggedDistance;\n\n    set(drawerRef.current, {\n      transform: `translate3d(0, ${newYValue}px, 0)`,\n    });\n  }\n\n  function getPercentageDragged(absDraggedDistance: number, isDraggingDown: boolean) {\n    if (!snapPoints || typeof activeSnapPointIndex !== 'number' || !snapPointsOffset || fadeFromIndex === undefined)\n      return null;\n\n    // If this is true we are dragging to a snap point that is supposed to have an overlay\n    const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n    const isOverlaySnapPointOrHigher = activeSnapPointIndex >= fadeFromIndex;\n\n    if (isOverlaySnapPointOrHigher && isDraggingDown) {\n      return 0;\n    }\n\n    // Don't animate, but still use this one if we are dragging away from the overlaySnapPoint\n    if (isOverlaySnapPoint && !isDraggingDown) return 1;\n    if (!shouldFade && !isOverlaySnapPoint) return null;\n\n    // Either fadeFrom index or the one before\n    const targetSnapPointIndex = isOverlaySnapPoint ? activeSnapPointIndex + 1 : activeSnapPointIndex - 1;\n\n    // Get the distance from overlaySnapPoint to the one before or vice-versa to calculate the opacity percentage accordingly\n    const snapPointDistance = isOverlaySnapPoint\n      ? snapPointsOffset[targetSnapPointIndex] - snapPointsOffset[targetSnapPointIndex - 1]\n      : snapPointsOffset[targetSnapPointIndex + 1] - snapPointsOffset[targetSnapPointIndex];\n\n    const percentageDragged = absDraggedDistance / Math.abs(snapPointDistance);\n\n    if (isOverlaySnapPoint) {\n      return 1 - percentageDragged;\n    } else {\n      return percentageDragged;\n    }\n  }\n\n  return {\n    isLastSnapPoint,\n    activeSnapPoint,\n    shouldFade,\n    getPercentageDragged,\n    setActiveSnapPoint,\n    activeSnapPointIndex,\n    onRelease,\n    onDrag,\n    snapPointsOffset,\n  };\n}\n", "interface Style {\n  [key: string]: string;\n}\n\nconst cache = new WeakMap();\n\nexport function isInView(el: HTMLElement): boolean {\n  const rect = el.getBoundingClientRect();\n\n  if (!window.visualViewport) return false;\n\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    // Need + 40 for safari detection\n    rect.bottom <= window.visualViewport.height - 40 &&\n    rect.right <= window.visualViewport.width\n  );\n}\n\nexport function set(el?: Element | HTMLElement | null, styles?: Style, ignoreCache = false) {\n  if (!el || !(el instanceof HTMLElement) || !styles) return;\n  let originalStyles: Style = {};\n\n  Object.entries(styles).forEach(([key, value]: [string, string]) => {\n    if (key.startsWith('--')) {\n      el.style.setProperty(key, value);\n      return;\n    }\n\n    originalStyles[key] = (el.style as any)[key];\n    (el.style as any)[key] = value;\n  });\n\n  if (ignoreCache) return;\n\n  cache.set(el, originalStyles);\n}\n\nexport function reset(el: Element | HTMLElement | null, prop?: string) {\n  if (!el || !(el instanceof HTMLElement)) return;\n  let originalStyles = cache.get(el);\n\n  if (!originalStyles) {\n    return;\n  }\n\n  if (prop) {\n    (el.style as any)[prop] = originalStyles[prop];\n  } else {\n    Object.entries(originalStyles).forEach(([key, value]) => {\n      (el.style as any)[key] = value;\n    });\n  }\n}\n\nexport function getTranslateY(element: HTMLElement): number | null {\n  const style = window.getComputedStyle(element);\n  const transform =\n    // @ts-ignore\n    style.transform || style.webkitTransform || style.mozTransform;\n  let mat = transform.match(/^matrix3d\\((.+)\\)$/);\n  if (mat) return parseFloat(mat[1].split(', ')[13]);\n  mat = transform.match(/^matrix\\((.+)\\)$/);\n  return mat ? parseFloat(mat[1].split(', ')[5]) : null;\n}\n\nexport function dampenValue(v: number) {\n  return 8 * (Math.log(v + 1) - 2);\n}\n", "export const TRANSITIONS = {\n  DURATION: 0.5,\n  EASE: [0.32, 0.72, 0, 1],\n};\n\nexport const VELOCITY_THRESHOLD = 0.4;\n", "// This code comes from https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\n\nimport React from 'react';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nfunction useUncontrolledState<T>({ defaultProp, onChange }: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\nexport function useControllableState<T>({ prop, defaultProp, onChange = () => {} }: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange],\n  );\n\n  return [value, setValue] as const;\n}\n"], "mappings": ";aAEA,UAAYA,MAAqB,yBACjC,OAAOC,MAAW,QCHlB,OAAOC,OAAW,QA4BX,IAAMC,GAAgBD,GAAM,cAAkC,CACnE,UAAW,CAAE,QAAS,IAAK,EAC3B,WAAY,CAAE,QAAS,IAAK,EAC5B,gBAAiB,IAAM,CAAC,EACxB,QAAS,IAAM,CAAC,EAChB,UAAW,IAAM,CAAC,EAClB,OAAQ,IAAM,CAAC,EACf,aAAc,IAAM,CAAC,EACrB,mBAAoB,IAAM,CAAC,EAC3B,gBAAiB,IAAM,CAAC,EACxB,SAAU,OACV,YAAa,GACb,OAAQ,GACR,eAAgB,CAAE,QAAS,EAAM,EACjC,iBAAkB,KAClB,WAAY,KACZ,MAAO,GACP,WAAY,GACZ,gBAAiB,KACjB,aAAc,IAAM,CAAC,EACrB,mBAAoB,IAAM,CAAC,EAC3B,QAAS,GACT,YAAa,IAAM,CAAC,EACpB,WAAY,IAAM,CAAC,CACrB,CAAC,EAEYE,GAAmB,IAAMF,GAAM,WAAWC,EAAa,ECrD3C,SAARE,GAA6BC,EAAK,CAAE,SAAAC,CAAS,EAAI,CAAC,EAAG,CAC1D,GAAI,CAACD,GAAO,OAAO,UAAa,YAAa,OAE7C,IAAME,EAAO,SAAS,MAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC,EAC/DC,EAAQ,SAAS,cAAc,OAAO,EAC5CA,EAAM,KAAO,WAETF,IAAa,OACXC,EAAK,WACPA,EAAK,aAAaC,EAAOD,EAAK,UAAU,EAK1CA,EAAK,YAAYC,CAAK,EAGpBA,EAAM,WACRA,EAAM,WAAW,QAAUH,EAE3BG,EAAM,YAAY,SAAS,eAAeH,CAAG,CAAC,CAElD,CCvB8BI,GAAY;AAAA,CAA00B,ECE93B,OAAS,aAAAC,GAAW,mBAAAC,OAAuB,QAEpC,IAAMC,GAA4B,OAAO,QAAW,YAAcD,GAAkBD,GAQ3F,SAASG,MAASC,EAA4C,CAC5D,MAAO,IAAIC,IAAgB,CACzB,QAASC,KAAYF,EACf,OAAOE,GAAa,YACtBA,EAAS,GAAGD,CAAI,CAGtB,CACF,CAEA,SAASE,IAA6B,CACpC,OAAOC,GAAa,MAAM,CAC5B,CAEA,SAASC,IAAgC,CACvC,OAAOD,GAAa,SAAS,CAC/B,CAMA,SAASE,IAA8B,CACrC,OACEC,GAAa,OAAO,GAEnBC,GAAM,GAAK,UAAU,eAAiB,CAE3C,CAEO,SAASC,IAA6B,CAC3C,OAAOC,GAAS,GAAKJ,GAAO,CAC9B,CAEA,SAASC,GAAaI,EAAiC,CACrD,OAAO,OAAO,QAAW,aAAe,OAAO,WAAa,KAAOA,EAAG,KAAK,OAAO,UAAU,QAAQ,EAAI,MAC1G,CAGA,IAAMC,GAAiB,OAAO,UAAa,aAAe,OAAO,eAE1D,SAASC,GAAaC,EAAwB,CACnD,IAAIC,EAAQ,OAAO,iBAAiBD,CAAI,EACxC,MAAO,gBAAgB,KAAKC,EAAM,SAAWA,EAAM,UAAYA,EAAM,SAAS,CAChF,CAEO,SAASC,GAAgBF,EAAwB,CAKtD,IAJID,GAAaC,CAAI,IACnBA,EAAOA,EAAK,eAGPA,GAAQ,CAACD,GAAaC,CAAI,GAC/BA,EAAOA,EAAK,cAGd,OAAOA,GAAQ,SAAS,kBAAoB,SAAS,eACvD,CAGA,IAAMG,GAAoB,IAAI,IAAI,CAChC,WACA,QACA,QACA,QACA,OACA,QACA,SACA,SACA,OACF,CAAC,EAGGC,GAAqB,EACrBC,GAOG,SAASC,GAAiBC,EAAgC,CAAC,EAAG,CACnE,GAAI,CAAE,WAAAC,CAAW,EAAID,EAErBE,GAA0B,IAAM,CAC9B,GAAI,CAAAD,EAIJ,OAAAJ,KACIA,KAAuB,IACrBT,GAAM,EACRU,GAAUK,GAA0B,EAEpCL,GAAUM,GAAsB,GAI7B,IAAM,CACXP,KACIA,KAAuB,GACzBC,GAAQ,CAEZ,CACF,EAAG,CAACG,CAAU,CAAC,CACjB,CAIA,SAASG,IAAwB,CAC/B,OAAOC,GACLC,GAAS,SAAS,gBAAiB,eAAgB,GAAG,OAAO,WAAa,SAAS,gBAAgB,eAAe,EAClHA,GAAS,SAAS,gBAAiB,WAAY,QAAQ,CACzD,CACF,CA4BA,SAASH,IAA4B,CACnC,IAAII,EACAC,EAAQ,EACRC,EAAgBC,GAAkB,CAEpCH,EAAaZ,GAAgBe,EAAE,MAAiB,EAC5C,EAAAH,IAAe,SAAS,iBAAmBA,IAAe,SAAS,QAIvEC,EAAQE,EAAE,eAAe,CAAC,EAAE,MAC9B,EAEIC,EAAeD,GAAkB,CAEnC,GAAI,CAACH,GAAcA,IAAe,SAAS,iBAAmBA,IAAe,SAAS,KAAM,CAC1FG,EAAE,eAAe,EACjB,OAOF,IAAIE,EAAIF,EAAE,eAAe,CAAC,EAAE,MACxBG,EAAYN,EAAW,UACvBO,EAASP,EAAW,aAAeA,EAAW,aAE9CO,IAAW,KAIVD,GAAa,GAAKD,EAAIJ,GAAWK,GAAaC,GAAUF,EAAIJ,IAC/DE,EAAE,eAAe,EAGnBF,EAAQI,EACV,EAEIG,EAAcL,GAAkB,CAClC,IAAIM,EAASN,EAAE,OAGXO,EAAQD,CAAM,GAAKA,IAAW,SAAS,gBACzCN,EAAE,eAAe,EAKjBM,EAAO,MAAM,UAAY,sBACzBA,EAAO,MAAM,EACb,sBAAsB,IAAM,CAC1BA,EAAO,MAAM,UAAY,EAC3B,CAAC,EAEL,EAEIE,EAAWR,GAAkB,CAC/B,IAAIM,EAASN,EAAE,OACXO,EAAQD,CAAM,IAKhBA,EAAO,MAAM,UAAY,sBACzB,sBAAsB,IAAM,CAC1BA,EAAO,MAAM,UAAY,GAIrBzB,KACEA,GAAe,OAAS,OAAO,YAGjC,sBAAsB,IAAM,CAC1B4B,GAAeH,CAAM,CACvB,CAAC,EAIDzB,GAAe,iBAAiB,SAAU,IAAM4B,GAAeH,CAAM,EAAG,CAAE,KAAM,EAAK,CAAC,EAG5F,CAAC,EAEL,EAEII,EAAiB,IAAM,CAGzB,OAAO,SAAS,EAAG,CAAC,CACtB,EAKIC,EAAU,OAAO,YACjBC,EAAU,OAAO,YAEjBC,EAAgBlB,GAClBC,GAAS,SAAS,gBAAiB,eAAgB,GAAG,OAAO,WAAa,SAAS,gBAAgB,eAAe,EAClHA,GAAS,SAAS,gBAAiB,WAAY,QAAQ,CAEzD,EAGA,OAAO,SAAS,EAAG,CAAC,EAEpB,IAAIkB,EAAenB,GACjBoB,EAAS,SAAU,aAAchB,EAAc,CAAE,QAAS,GAAO,QAAS,EAAK,CAAC,EAChFgB,EAAS,SAAU,YAAad,EAAa,CAAE,QAAS,GAAO,QAAS,EAAK,CAAC,EAC9Ec,EAAS,SAAU,WAAYV,EAAY,CAAE,QAAS,GAAO,QAAS,EAAK,CAAC,EAC5EU,EAAS,SAAU,QAASP,EAAS,EAAI,EACzCO,EAAS,OAAQ,SAAUL,CAAc,CAC3C,EAEA,MAAO,IAAM,CAEXG,EAAc,EACdC,EAAa,EACb,OAAO,SAASH,EAASC,CAAO,CAClC,CACF,CAGA,SAAShB,GAASoB,EAAsBhC,EAAeiC,EAAe,CACpE,IAAIC,EAAMF,EAAQ,MAAMhC,CAAK,EAC7B,OAAAgC,EAAQ,MAAMhC,CAAK,EAAIiC,EAEhB,IAAM,CACXD,EAAQ,MAAMhC,CAAK,EAAIkC,CACzB,CACF,CAGA,SAASH,EACPT,EACAa,EACAC,EACA9B,EACA,CAEA,OAAAgB,EAAO,iBAAiBa,EAAOC,EAAS9B,CAAO,EAExC,IAAM,CAEXgB,EAAO,oBAAoBa,EAAOC,EAAS9B,CAAO,CACpD,CACF,CAEA,SAASmB,GAAeH,EAAiB,CACvC,IAAIe,EAAO,SAAS,kBAAoB,SAAS,gBACjD,KAAOf,GAAUA,IAAWe,GAAM,CAEhC,IAAIxB,EAAaZ,GAAgBqB,CAAM,EACvC,GAAIT,IAAe,SAAS,iBAAmBA,IAAe,SAAS,MAAQA,IAAeS,EAAQ,CACpG,IAAIgB,EAAgBzB,EAAW,sBAAsB,EAAE,IACnD0B,EAAYjB,EAAO,sBAAsB,EAAE,IAC3CkB,EAAelB,EAAO,sBAAsB,EAAE,OAC5CmB,EAAiB5B,EAAW,sBAAsB,EAAE,OAEtD2B,EAAeC,IACjB5B,EAAW,WAAa0B,EAAYD,GAKxChB,EAAST,EAAW,cAExB,CAEO,SAASU,EAAQD,EAAiB,CACvC,OACGA,aAAkB,kBAAoB,CAACpB,GAAkB,IAAIoB,EAAO,IAAI,GACzEA,aAAkB,qBACjBA,aAAkB,aAAeA,EAAO,iBAE7C,CCxUA,UAAYoB,OAAW,QAQvB,SAASC,GAAUC,EAAqBC,EAAU,CAC5C,OAAOD,GAAQ,WACjBA,EAAIC,CAAK,EACAD,GAAQ,OAChBA,EAAkC,QAAUC,EAEjD,CAMA,SAASC,MAAkBC,EAAwB,CACjD,OAAQC,GAAYD,EAAK,QAASH,GAAQD,GAAOC,EAAKI,CAAI,CAAC,CAC7D,CAMA,SAASC,MAAsBF,EAAwB,CAErD,OAAa,eAAYD,GAAY,GAAGC,CAAI,EAAGA,CAAI,CACrD,CCjCA,OAAOG,OAAW,QAElB,IAAIC,EAAsD,KAEnD,SAASC,GAAiB,CAC/B,OAAAC,EACA,MAAAC,EACA,OAAAC,EACA,cAAAC,CACF,EAKG,CACD,GAAM,CAACC,EAAWC,CAAY,EAAIR,GAAM,SAAS,OAAO,QAAW,YAAc,OAAO,SAAS,KAAO,EAAE,EACpGS,EAAYT,GAAM,OAAO,CAAC,EAEhC,SAASU,GAAmB,CAE1B,GAAIT,IAAyB,MAAQE,EAAQ,CAC3CF,EAAuB,CACrB,SAAU,SAAS,KAAK,MAAM,SAC9B,IAAK,SAAS,KAAK,MAAM,IACzB,KAAM,SAAS,KAAK,MAAM,KAC1B,OAAQ,SAAS,KAAK,MAAM,MAC9B,EAGA,GAAM,CAAE,QAAAU,EAAS,YAAAC,CAAY,EAAI,OAEjC,SAAS,KAAK,MAAM,YAAY,WAAY,QAAS,WAAW,EAChE,SAAS,KAAK,MAAM,IAAM,GAAG,CAACH,EAAU,YACxC,SAAS,KAAK,MAAM,KAAO,GAAG,CAACE,MAC/B,SAAS,KAAK,MAAM,MAAQ,MAC5B,SAAS,KAAK,MAAM,OAAS,OAE7B,WACE,IACE,sBAAsB,IAAM,CAE1B,IAAME,EAAkBD,EAAc,OAAO,YACzCC,GAAmBJ,EAAU,SAAWG,IAE1C,SAAS,KAAK,MAAM,IAAM,GAAG,EAAEH,EAAU,QAAUI,OAEvD,CAAC,EACH,GACF,EAEJ,CAEA,SAASC,GAAyB,CAChC,GAAIb,IAAyB,KAAM,CAEjC,IAAMc,EAAI,CAAC,SAAS,SAAS,KAAK,MAAM,IAAK,EAAE,EACzCC,EAAI,CAAC,SAAS,SAAS,KAAK,MAAM,KAAM,EAAE,EAGhD,SAAS,KAAK,MAAM,SAAWf,EAAqB,SACpD,SAAS,KAAK,MAAM,IAAMA,EAAqB,IAC/C,SAAS,KAAK,MAAM,KAAOA,EAAqB,KAChD,SAAS,KAAK,MAAM,OAASA,EAAqB,OAClD,SAAS,KAAK,MAAM,MAAQ,QAE5B,sBAAsB,IAAM,CAC1B,GAAIM,IAAc,OAAO,SAAS,KAAM,CACtCC,EAAa,OAAO,SAAS,IAAI,EACjC,OAGF,OAAO,SAASQ,EAAGD,CAAC,CACtB,CAAC,EAEDd,EAAuB,KAE3B,CAEA,OAAAD,GAAM,UAAU,IAAM,CACpB,SAASiB,GAAW,CAClBR,EAAU,QAAU,OAAO,OAC7B,CAEA,OAAAQ,EAAS,EAET,OAAO,iBAAiB,SAAUA,CAAQ,EAEnC,IAAM,CACX,OAAO,oBAAoB,SAAUA,CAAQ,CAC/C,CACF,EAAG,CAAC,CAAC,EAELjB,GAAM,UAAU,IAAM,CAChBK,GAAU,CAACC,IAEXH,GACFO,EAAiB,EAEZN,GACH,WAAW,IAAM,CACfU,EAAuB,CACzB,EAAG,GAAG,GAGRA,EAAuB,EAE3B,EAAG,CAACX,EAAQG,EAAeC,CAAS,CAAC,EAE9B,CAAE,uBAAAO,CAAuB,CAClC,CC7GA,OAAOI,MAAW,QCIlB,IAAMC,GAAQ,IAAI,QAgBX,SAASC,EAAIC,EAAmCC,EAAgBC,EAAc,GAAO,CAC1F,GAAI,CAACF,GAAM,EAAEA,aAAc,cAAgB,CAACC,EAAQ,OACpD,IAAIE,EAAwB,CAAC,EAE7B,OAAO,QAAQF,CAAM,EAAE,QAAQ,CAAC,CAACG,EAAKC,CAAK,IAAwB,CACjE,GAAID,EAAI,WAAW,IAAI,EAAG,CACxBJ,EAAG,MAAM,YAAYI,EAAKC,CAAK,EAC/B,OAGFF,EAAeC,CAAG,EAAKJ,EAAG,MAAcI,CAAG,EAC1CJ,EAAG,MAAcI,CAAG,EAAIC,CAC3B,CAAC,EAEG,CAAAH,GAEJI,GAAM,IAAIN,EAAIG,CAAc,CAC9B,CAEO,SAASI,EAAMP,EAAkCQ,EAAe,CACrE,GAAI,CAACR,GAAM,EAAEA,aAAc,aAAc,OACzC,IAAIG,EAAiBG,GAAM,IAAIN,CAAE,EAE5BG,IAIDK,EACDR,EAAG,MAAcQ,CAAI,EAAIL,EAAeK,CAAI,EAE7C,OAAO,QAAQL,CAAc,EAAE,QAAQ,CAAC,CAACC,EAAKC,CAAK,IAAM,CACtDL,EAAG,MAAcI,CAAG,EAAIC,CAC3B,CAAC,EAEL,CAEO,SAASI,GAAcC,EAAqC,CACjE,IAAMC,EAAQ,OAAO,iBAAiBD,CAAO,EACvCE,EAEJD,EAAM,WAAaA,EAAM,iBAAmBA,EAAM,aAChDE,EAAMD,EAAU,MAAM,oBAAoB,EAC9C,OAAIC,EAAY,WAAWA,EAAI,CAAC,EAAE,MAAM,IAAI,EAAE,EAAE,CAAC,GACjDA,EAAMD,EAAU,MAAM,kBAAkB,EACjCC,EAAM,WAAWA,EAAI,CAAC,EAAE,MAAM,IAAI,EAAE,CAAC,CAAC,EAAI,KACnD,CAEO,SAASC,GAAYC,EAAW,CACrC,MAAO,IAAK,KAAK,IAAIA,EAAI,CAAC,EAAI,EAChC,CCrEO,IAAMC,EAAc,CACzB,SAAU,GACV,KAAM,CAAC,IAAM,IAAM,EAAG,CAAC,CACzB,EAEaC,GAAqB,GCHlC,OAAOC,MAAW,QAUlB,SAASC,GAAkDC,EAA4B,CACrF,IAAMC,EAAcH,EAAM,OAAOE,CAAQ,EAEzC,OAAAF,EAAM,UAAU,IAAM,CACpBG,EAAY,QAAUD,CACxB,CAAC,EAGMF,EAAM,QAAQ,IAAO,IAAII,IAAM,CApBxC,IAAAC,EAoB2C,OAAAA,EAAAF,EAAY,UAAZ,YAAAE,EAAA,KAAAF,EAAsB,GAAGC,IAAa,CAAC,CAAC,CACnF,CAEA,SAASE,GAAwB,CAAE,YAAAC,EAAa,SAAAC,CAAS,EAAgD,CACvG,IAAMC,EAAoBT,EAAM,SAAwBO,CAAW,EAC7D,CAACG,CAAK,EAAID,EACVE,EAAeX,EAAM,OAAOU,CAAK,EACjCE,EAAeX,GAAeO,CAAQ,EAE5C,OAAAR,EAAM,UAAU,IAAM,CAChBW,EAAa,UAAYD,IAC3BE,EAAaF,CAAU,EACvBC,EAAa,QAAUD,EAE3B,EAAG,CAACA,EAAOC,EAAcC,CAAY,CAAC,EAE/BH,CACT,CACO,SAASI,GAAwB,CAAE,KAAAC,EAAM,YAAAP,EAAa,SAAAC,EAAW,IAAM,CAAC,CAAE,EAAkC,CACjH,GAAM,CAACO,EAAkBC,CAAmB,EAAIV,GAAqB,CAAE,YAAAC,EAAa,SAAAC,CAAS,CAAC,EACxFS,EAAeH,IAAS,OACxBJ,EAAQO,EAAeH,EAAOC,EAC9BH,EAAeX,GAAeO,CAAQ,EAEtCU,EAAgElB,EAAM,YACzEmB,GAAc,CACb,GAAIF,EAAc,CAEhB,IAAMP,EAAQ,OAAOS,GAAc,WADpBA,EACwCL,CAAI,EAAIK,EAC3DT,IAAUI,GAAMF,EAAaF,CAAU,OAE3CM,EAAoBG,CAAS,CAEjC,EACA,CAACF,EAAcH,EAAME,EAAqBJ,CAAY,CACxD,EAEA,MAAO,CAACF,EAAOQ,CAAQ,CACzB,CHrDO,SAASE,GAAc,CAC5B,oBAAAC,EACA,uBAAAC,EACA,WAAAC,EACA,UAAAC,EACA,WAAAC,EACA,cAAAC,EACA,kBAAAC,CACF,EAQG,CACD,GAAM,CAACC,EAAiBC,CAAkB,EAAIC,GAA6C,CACzF,KAAMT,EACN,YAAaE,GAAA,YAAAA,EAAa,GAC1B,SAAUD,CACZ,CAAC,EAEKS,EAAkBC,EAAM,QAC5B,IAAMJ,KAAoBL,GAAA,YAAAA,EAAaA,EAAW,OAAS,IAC3D,CAACA,EAAYK,CAAe,CAC9B,EAEMK,EACHV,GAAcA,EAAW,OAAS,GAAKG,GAAiBH,EAAWG,CAAa,IAAME,GACvF,CAACL,EAEGW,EAAuBF,EAAM,QACjC,IAAG,CAtCP,IAAAG,EAsCU,OAAAA,EAAAZ,GAAA,YAAAA,EAAY,UAAWa,GAAcA,IAAcR,KAAnD,KAAAO,EAAuE,MAC7E,CAACZ,EAAYK,CAAe,CAC9B,EAEMS,EAAmBL,EAAM,QAC7B,IAAG,CA3CP,IAAAG,EA4CM,OAAAA,EAAAZ,GAAA,YAAAA,EAAY,IAAKa,GAAc,CAC7B,IAAME,EAAY,OAAO,QAAW,YAC9BC,EAAO,OAAOH,GAAc,SAC9BI,EAAoB,EAEpBD,IACFC,EAAoB,SAASJ,EAAW,EAAE,GAG5C,IAAMK,EAASF,EAAOC,EAAoBF,EAAYF,EAAY,OAAO,YAAc,EAEvF,OAAIE,EACK,OAAO,YAAcG,EAGvBA,CACT,KAhBA,KAAAN,EAgBM,CAAC,GACT,CAACZ,CAAU,CACb,EAEMmB,EAAwBV,EAAM,QAClC,IAAOE,IAAyB,KAAOG,GAAA,YAAAA,EAAmBH,GAAwB,KAClF,CAACG,EAAkBH,CAAoB,CACzC,EAEMS,EAAcX,EAAM,YACvBS,GAAmB,CAtExB,IAAAN,EAuEM,IAAMS,GAAoBT,EAAAE,GAAA,YAAAA,EAAkB,UAAWQ,GAAoBA,IAAoBJ,KAArE,KAAAN,EAAgF,KAC1GR,EAAkBiB,CAAiB,EACnCE,EAAItB,EAAU,QAAS,CACrB,WAAY,aAAauB,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,KACxF,UAAW,kBAAkBN,SAC/B,CAAC,EAGCJ,GACAO,IAAsBP,EAAiB,OAAS,GAChDO,IAAsBlB,EAEtBoB,EAAIrB,EAAW,QAAS,CACtB,WAAY,WAAWsB,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,KACtF,QAAS,GACX,CAAC,EAEDD,EAAIrB,EAAW,QAAS,CACtB,WAAY,WAAWsB,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,KACtF,QAAS,GACX,CAAC,EAGHlB,EAAmBe,IAAsB,KAAOrB,GAAA,YAAAA,EAAaqB,GAAqB,IAAI,CACxF,EACA,CAACpB,EAAU,QAASD,EAAYc,EAAkBX,EAAeD,EAAYI,CAAkB,CACjG,EAEAG,EAAM,UAAU,IAAM,CAnGxB,IAAAG,EAoGI,GAAId,EAAqB,CACvB,IAAM2B,GAAWb,EAAAZ,GAAA,YAAAA,EAAY,UAAWa,GAAcA,IAAcf,KAAnD,KAAAc,EAA2E,KACxFE,GAAoBW,GAAY,OAAOX,EAAiBW,CAAQ,GAAM,UACxEL,EAAYN,EAAiBW,CAAQ,CAAW,EAGtD,EAAG,CAAC3B,EAAqBE,EAAYc,EAAkBM,CAAW,CAAC,EAEnE,SAASM,EAAU,CACjB,gBAAAC,EACA,YAAAC,EACA,SAAAC,CACF,EAIG,CACD,GAAI1B,IAAkB,OAAW,OAEjC,IAAM2B,EAAkBX,EAAwBQ,EAC1CI,EAAqBpB,IAAyBR,EAAgB,EAC9D6B,EAAUrB,IAAyB,EAQzC,GANIoB,GACFR,EAAIrB,EAAW,QAAS,CACtB,WAAY,WAAWsB,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,IACxF,CAAC,EAGCK,EAAW,GAAKF,EAAkB,EAAG,CACvCC,EAAY,EACZ,OAGF,GAAIC,EAAW,GAAKF,EAAkB,GAAKb,GAAoBd,EAAY,CACzEoB,EAAYN,EAAiBd,EAAW,OAAS,CAAC,CAAW,EAC7D,OAIF,IAAMiC,EAAmBnB,GAAA,YAAAA,EAAkB,OAAO,CAACoB,EAAMC,IACnD,OAAOD,GAAS,UAAY,OAAOC,GAAS,SAAiBD,EAE1D,KAAK,IAAIC,EAAOL,CAAe,EAAI,KAAK,IAAII,EAAOJ,CAAe,EAAIK,EAAOD,GAGtF,GAAIL,EAAWO,IAAsB,KAAK,IAAIT,CAAe,EAAI,OAAO,YAAc,GAAK,CAEzF,IAAMU,EAAgBV,EAAkB,EAAI,EAAI,GAGhD,GAAIU,EAAgB,GAAK7B,EAAiB,CACxCY,EAAYN,EAAiBd,EAAW,OAAS,CAAC,CAAC,EACnD,OAOF,GAJIgC,GAAWK,EAAgB,GAC7BT,EAAY,EAGVjB,IAAyB,KAAM,OAEnCS,EAAYN,EAAiBH,EAAuB0B,CAAa,CAAC,EAClE,OAGFjB,EAAYa,CAAgB,CAC9B,CAEA,SAASK,EAAO,CAAE,gBAAAX,CAAgB,EAAgC,CAChE,GAAIR,IAA0B,KAAM,OACpC,IAAMoB,EAAYpB,EAAwBQ,EAE1CJ,EAAItB,EAAU,QAAS,CACrB,UAAW,kBAAkBsC,SAC/B,CAAC,CACH,CAEA,SAASC,EAAqBC,EAA4BC,EAAyB,CACjF,GAAI,CAAC1C,GAAc,OAAOW,GAAyB,UAAY,CAACG,GAAoBX,IAAkB,OACpG,OAAO,KAGT,IAAM4B,EAAqBpB,IAAyBR,EAAgB,EAGpE,GAFmCQ,GAAwBR,GAEzBuC,EAChC,MAAO,GAIT,GAAIX,GAAsB,CAACW,EAAgB,MAAO,GAClD,GAAI,CAAChC,GAAc,CAACqB,EAAoB,OAAO,KAG/C,IAAMY,EAAuBZ,EAAqBpB,EAAuB,EAAIA,EAAuB,EAG9FiC,EAAoBb,EACtBjB,EAAiB6B,CAAoB,EAAI7B,EAAiB6B,EAAuB,CAAC,EAClF7B,EAAiB6B,EAAuB,CAAC,EAAI7B,EAAiB6B,CAAoB,EAEhFE,EAAoBJ,EAAqB,KAAK,IAAIG,CAAiB,EAEzE,OAAIb,EACK,EAAIc,EAEJA,CAEX,CAEA,MAAO,CACL,gBAAArC,EACA,gBAAAH,EACA,WAAAK,EACA,qBAAA8B,EACA,mBAAAlC,EACA,qBAAAK,EACA,UAAAe,EACA,OAAAY,EACA,iBAAAxB,CACF,CACF,CPjNA,IAAMgC,GAAkB,IAElBC,GAAsB,IAEtBC,GAAgB,EAEhBC,EAAsB,GAEtBC,GAAoB,GAEpBC,GAAa,gBA8BnB,SAASC,GAAK,CACZ,KAAMC,EACN,aAAAC,EACA,SAAAC,EACA,sBAAAC,EACA,OAAQC,EACR,UAAWC,EACX,WAAAC,EACA,OAAAC,EACA,eAAAC,EAAiBf,GACjB,kBAAAgB,EAAoBf,GACpB,YAAAgB,EAAc,GACd,cAAAC,EAAgBL,GAAcA,EAAW,OAAS,EAClD,gBAAiBM,EACjB,mBAAoBC,EACpB,MAAAC,EACA,MAAAC,EAAQ,GACR,QAAAC,CACF,EAAgB,CAvEhB,IAAAC,GAwEE,GAAM,CAACC,EAAS,GAAOC,CAAS,EAAIC,EAAM,SAAkB,EAAK,EAC3D,CAACC,EAAeC,CAAgB,EAAIF,EAAM,SAAkB,EAAK,EAEjE,CAACG,EAASC,CAAU,EAAIJ,EAAM,SAAkB,EAAK,EACrD,CAACK,EAASC,CAAU,EAAIN,EAAM,SAAkB,EAAK,EACrD,CAACO,EAAYC,CAAa,EAAIR,EAAM,SAAkB,EAAK,EAC3D,CAACS,GAAcC,EAAe,EAAIV,EAAM,SAAkB,EAAK,EAC/DW,EAAaX,EAAM,OAAuB,IAAI,EAC9CY,GAAWZ,EAAM,OAAoB,IAAI,EACzCa,GAAgBb,EAAM,OAAoB,IAAI,EAC9Cc,GAAcd,EAAM,OAAoB,IAAI,EAC5Ce,EAAwBf,EAAM,OAAoB,IAAI,EACtDgB,EAAkBhB,EAAM,OAAgB,EAAK,EAC7CiB,GAAwBjB,EAAM,OAA8B,IAAI,EAChEkB,GAAgBlB,EAAM,OAAO,CAAC,EAC9BmB,EAAiBnB,EAAM,OAAO,EAAK,EACnCoB,GAA0BpB,EAAM,OAAO,CAAC,EACxCqB,EAAYrB,EAAM,OAAuB,IAAI,EAC7CsB,GAAkBtB,EAAM,SAAOH,GAAAwB,EAAU,UAAV,YAAAxB,GAAmB,wBAAwB,SAAU,CAAC,EACrF0B,GAAsBvB,EAAM,OAAO,CAAC,EAEpCwB,GAAoBxB,EAAM,YAAayB,GAAiC,CAExEvC,GAAcuC,IAAyBC,EAAiB,OAAS,IAAGd,GAAS,QAAU,IAAI,KACjG,EAAG,CAAC,CAAC,EAEC,CACJ,gBAAAe,GACA,qBAAAF,EACA,mBAAAG,GACA,UAAWC,GACX,iBAAAH,EACA,OAAQI,GACR,WAAAC,GACA,qBAAsBC,EACxB,EAAIC,GAAc,CAChB,WAAA/C,EACA,oBAAAM,EACA,uBAAAC,EACA,UAAA4B,EACA,cAAA9B,EACA,WAAAoB,EACA,kBAAAa,EACF,CAAC,EAEDU,GAAiB,CACf,WAAY,CAACpC,GAAUS,GAAc,CAACZ,GAASc,IAAgB,CAACR,CAClE,CAAC,EAED,GAAM,CAAE,uBAAAkC,EAAuB,EAAIC,GAAiB,CAClD,OAAAtC,EACA,MAAAH,EACA,OAAAR,EACA,cAAAc,CACF,CAAC,EAED,SAASoC,IAAW,CAClB,OAAQ,OAAO,WAAa5D,IAAqB,OAAO,UAC1D,CAEA,SAAS6D,GAAQC,EAA2C,CApI9D,IAAA1C,EAqIQ,CAACP,GAAe,CAACJ,GACjBmC,EAAU,SAAW,CAACA,EAAU,QAAQ,SAASkB,EAAM,MAAc,IACzEjB,GAAgB,UAAUzB,EAAAwB,EAAU,UAAV,YAAAxB,EAAmB,wBAAwB,SAAU,EAC/EW,EAAc,EAAI,EAClBK,GAAc,QAAU,IAAI,KAGxB2B,GAAM,GACR,OAAO,iBAAiB,WAAY,IAAOxB,EAAgB,QAAU,GAAQ,CAAE,KAAM,EAAK,CAAC,EAG5FuB,EAAM,OAAuB,kBAAkBA,EAAM,SAAS,EAE/DrB,GAAc,QAAUqB,EAAM,QAChC,CAEA,SAASE,GAAWC,EAAiBC,EAAyB,CArJhE,IAAA9C,EAsJI,IAAI+C,EAAUF,EACRG,EAAO,IAAI,KACXC,GAAkBjD,EAAA,OAAO,aAAa,IAApB,YAAAA,EAAuB,WACzCkD,EAAc1B,EAAU,QAAU2B,GAAc3B,EAAU,OAAO,EAAI,KAG3E,GAAIT,GAAS,SAAWiC,EAAK,QAAQ,EAAIjC,GAAS,QAAQ,QAAQ,EAAI,IACpE,MAAO,GAGT,GAAImC,EAAc,EAChB,MAAO,GAIT,GAAID,GAAmBA,EAAgB,OAAS,EAC9C,MAAO,GAIT,GACE/B,EAAsB,SACtB8B,EAAK,QAAQ,EAAI9B,EAAsB,QAAQ,QAAQ,EAAI1B,GAC3D0D,IAAgB,EAEhB,OAAAhC,EAAsB,QAAU,IAAI,KAC7B,GAGT,GAAI4B,EACF,OAAA5B,EAAsB,QAAU,IAAI,KAG7B,GAIT,KAAO6B,GAAS,CAEd,GAAIA,EAAQ,aAAeA,EAAQ,aAAc,CAC/C,GAAIA,EAAQ,YAAc,EACxB,OAAA7B,EAAsB,QAAU,IAAI,KAG7B,GAGT,GAAI6B,EAAQ,aAAa,MAAM,IAAM,SACnC,MAAO,GAKXA,EAAUA,EAAQ,WAIpB,MAAO,EACT,CAEA,SAASK,GAAOV,EAA2C,CAEzD,GAAIhC,EAAY,CACd,IAAM2C,EAAkBhC,GAAc,QAAUqB,EAAM,QAChDI,EAAiBO,EAAkB,EAKzC,GAFIhE,GAAcuC,IAAyB,GAAK,CAACnC,GAE7C,CAAC0B,EAAgB,SAAW,CAACyB,GAAWF,EAAM,OAAQI,CAAc,EAAG,OAiB3E,GAhBAtB,EAAU,QAAQ,UAAU,IAAI3C,EAAU,EAE1CsC,EAAgB,QAAU,GAC1BmC,EAAI9B,EAAU,QAAS,CACrB,WAAY,MACd,CAAC,EAED8B,EAAIxC,EAAW,QAAS,CACtB,WAAY,MACd,CAAC,EAEGzB,GACF4C,GAAiB,CAAE,gBAAAoB,CAAgB,CAAC,EAIlCA,EAAkB,GAAK,CAAChE,EAAY,CACtC,IAAMkE,EAA0BC,GAAYH,CAAe,EAE3DC,EAAI9B,EAAU,QAAS,CACrB,UAAW,kBAAkB,KAAK,IAAI+B,EAA0B,GAAI,CAAC,SACvE,CAAC,EACD,OAIF,IAAME,EAAqB,KAAK,IAAIJ,CAAe,EAC7CK,EAAU,SAAS,cAAc,uBAAuB,EAE1DC,EAAoBF,EAAqBhC,GAAgB,QACvDmC,EAA6BzB,GAA+BsB,EAAoBX,CAAc,EAEhGc,IAA+B,OACjCD,EAAoBC,GAGtB,IAAMC,GAAe,EAAIF,EAezB,IAbIzB,IAAexC,GAAiBkC,IAAyBlC,EAAgB,KAC3EP,GAAA,MAAAA,EAAauD,EAAOiB,GAEpBL,EACExC,EAAW,QACX,CACE,QAAS,GAAG+C,KACZ,WAAY,MACd,EACA,EACF,GAGEH,GAAW5C,EAAW,SAAW5B,EAAuB,CAE1D,IAAM4E,EAAa,KAAK,IAAItB,GAAS,EAAImB,GAAqB,EAAInB,GAAS,GAAI,CAAC,EAC1EuB,GAAoB,EAAIJ,EAAoB,EAE5CK,GAAkB,KAAK,IAAI,EAAG,GAAKL,EAAoB,EAAE,EAE/DL,EACEI,EACA,CACE,aAAc,GAAGK,OACjB,UAAW,SAASD,qBAA8BE,WAClD,WAAY,MACd,EACA,EACF,EAGG3E,GACHiE,EAAI9B,EAAU,QAAS,CACrB,UAAW,kBAAkBiC,SAC/B,CAAC,EAGP,CAEAtD,EAAM,UAAU,IACP,IAAM,CACX8D,GAAgB,EAAK,EACrB3B,GAAuB,CACzB,EACC,CAAC,CAAC,EAELnC,EAAM,UAAU,IAAM,CAhTxB,IAAAH,EAiTI,SAASkE,GAAyB,CAjTtC,IAAAlE,EAkTM,GAAI,CAACwB,EAAU,QAAS,OAExB,IAAM2C,EAAiB,SAAS,cAChC,GAAIC,EAAQD,CAAc,GAAK7C,EAAe,QAAS,CACrD,IAAM+C,IAAuBrE,EAAA,OAAO,iBAAP,YAAAA,EAAuB,SAAU,EAE1DsE,EAAkB,OAAO,YAAcD,EACrCE,EAAe/C,EAAU,QAAQ,sBAAsB,EAAE,QAAU,EACpEE,GAAoB,UACvBA,GAAoB,QAAU6C,GAEhC,IAAMC,GAAgBhD,EAAU,QAAQ,sBAAsB,EAAE,IAOhE,GAJI,KAAK,IAAID,GAAwB,QAAU+C,CAAe,EAAI,KAChEhD,EAAe,QAAU,CAACA,EAAe,SAGvCjC,GAAcA,EAAW,OAAS,GAAKwC,GAAoBD,EAAsB,CACnF,IAAM6C,EAAwB5C,EAAiBD,CAAoB,GAAK,EACxE0C,GAAmBG,EAKrB,GAFAlD,GAAwB,QAAU+C,EAE9BC,EAAeF,GAAwB/C,EAAe,QAAS,CACjE,IAAMoD,EAASlD,EAAU,QAAQ,sBAAsB,EAAE,OACrDmD,GAAkBD,EAElBA,EAASL,IACXM,GAAkBN,EAAuBzF,IAGvCiB,EACF2B,EAAU,QAAQ,MAAM,OAAS,GAAGkD,EAAS,KAAK,IAAIJ,EAAiB,CAAC,MAExE9C,EAAU,QAAQ,MAAM,OAAS,GAAG,KAAK,IAAImD,GAAiBN,EAAuBG,EAAa,WAGpGhD,EAAU,QAAQ,MAAM,OAAS,GAAGE,GAAoB,YAGtDrC,GAAcA,EAAW,OAAS,GAAK,CAACiC,EAAe,QACzDE,EAAU,QAAQ,MAAM,OAAS,MAGjCA,EAAU,QAAQ,MAAM,OAAS,GAAG,KAAK,IAAI8C,EAAiB,CAAC,MAGrE,CAEA,OAAAtE,EAAA,OAAO,iBAAP,MAAAA,EAAuB,iBAAiB,SAAUkE,GAC3C,IAAG,CAtWd,IAAAlE,EAsWiB,OAAAA,EAAA,OAAO,iBAAP,YAAAA,EAAuB,oBAAoB,SAAUkE,GACpE,EAAG,CAACtC,EAAsBvC,EAAYwC,CAAgB,CAAC,EAEvD,SAAS+C,GAAc,CAChBpD,EAAU,UAEfzB,GAAA,MAAAA,IACIyB,EAAU,UACZ8B,EAAI9B,EAAU,QAAS,CACrB,UAAW,0BACX,WAAY,aAAaqD,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,IAC1F,CAAC,EAEDvB,EAAIxC,EAAW,QAAS,CACtB,QAAS,IACT,WAAY,WAAW+D,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,IACxF,CAAC,EAEDZ,GAAgB,EAAK,GAGvB,WAAW,IAAM,CACf1D,EAAW,EAAK,EAChBL,EAAU,EAAK,CACjB,EAAG,GAAG,EAEN,WAAW,IAAM,CACXb,GACF0C,GAAmB1C,EAAW,CAAC,CAAC,CAEpC,EAAG,GAAG,EACR,CAEAc,EAAM,UAAU,IAAM,CACpB,GAAI,CAACF,GAAUf,EAAuB,CAEpC,IAAM4F,EAAK,WAAW,IAAM,CAC1BC,EAAM,SAAS,IAAI,CACrB,EAAG,GAAG,EAEN,MAAO,IAAM,aAAaD,CAAE,EAEhC,EAAG,CAAC7E,EAAQf,CAAqB,CAAC,EAGlCiB,EAAM,UAAU,IAAM,CAChBpB,GACFmB,EAAU,EAAI,EACdG,EAAiB,EAAI,GAErBuE,EAAY,CAEhB,EAAG,CAAC7F,CAAQ,CAAC,EAGboB,EAAM,UAAU,IAAM,CAChBK,IACFxB,GAAA,MAAAA,EAAeiB,GAEnB,EAAG,CAACA,CAAM,CAAC,EAEXE,EAAM,UAAU,IAAM,CACpBM,EAAW,EAAI,CACjB,EAAG,CAAC,CAAC,EAEL,SAASuE,IAAc,CACrB,GAAI,CAACxD,EAAU,QAAS,OACxB,IAAMkC,EAAU,SAAS,cAAc,uBAAuB,EACxDuB,EAAqB9B,GAAc3B,EAAU,OAAO,EAE1D8B,EAAI9B,EAAU,QAAS,CACrB,UAAW,uBACX,WAAY,aAAaqD,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,IAC1F,CAAC,EAEDvB,EAAIxC,EAAW,QAAS,CACtB,WAAY,WAAW+D,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,KACtF,QAAS,GACX,CAAC,EAGG3F,GAAyB+F,GAAsBA,EAAqB,GAAKhF,GAC3EqD,EACEI,EACA,CACE,aAAc,GAAGhF,OACjB,SAAU,SACV,UAAW,SAAS8D,GAAS,8DAC7B,gBAAiB,MACjB,mBAAoB,2BACpB,mBAAoB,GAAGqC,EAAY,YACnC,yBAA0B,gBAAgBA,EAAY,KAAK,KAAK,GAAG,IACrE,EACA,EACF,CAEJ,CAEA,SAASK,GAAUxC,EAA2C,CAC5D,GAAI,CAAChC,GAAc,CAACc,EAAU,QAAS,OACnCL,EAAgB,SAAWiD,EAAQ1B,EAAM,MAAqB,GAE/DA,EAAM,OAA4B,KAAK,EAE1ClB,EAAU,QAAQ,UAAU,OAAO3C,EAAU,EAC7CsC,EAAgB,QAAU,GAC1BR,EAAc,EAAK,EACnBM,GAAY,QAAU,IAAI,KAC1B,IAAMiC,EAAcC,GAAc3B,EAAU,OAAO,EAInD,GAFI,CAACoB,GAAWF,EAAM,OAAQ,EAAK,GAAK,CAACQ,GAAe,OAAO,MAAMA,CAAW,GAE5ElC,GAAc,UAAY,KAAM,OAEpC,IAAMmE,EAAIzC,EAAM,QAEV0C,EAAYnE,GAAY,QAAQ,QAAQ,EAAID,GAAc,QAAQ,QAAQ,EAC1EqE,EAAYhE,GAAc,QAAU8D,EACpCG,EAAW,KAAK,IAAID,CAAS,EAAID,EAWvC,GATIE,EAAW,MAEbzE,GAAgB,EAAI,EAEpB,WAAW,IAAM,CACfA,GAAgB,EAAK,CACvB,EAAG,GAAG,GAGJxB,EAAY,CACd2C,GAAoB,CAClB,gBAAiBqD,EACjB,YAAAT,EACA,SAAAU,CACF,CAAC,EACD,OAIF,GAAID,EAAY,EAAG,CACjBL,GAAY,EACZ5F,GAAA,MAAAA,EAAgBsD,EAAO,IACvB,OAGF,GAAI4C,EAAWC,GAAoB,CACjCX,EAAY,EACZxF,GAAA,MAAAA,EAAgBsD,EAAO,IACvB,OAGF,IAAM8C,EAAsB,KAAK,IAAIhE,EAAU,QAAQ,sBAAsB,EAAE,QAAU,EAAG,OAAO,WAAW,EAE9G,GAAI0B,GAAesC,EAAsBjG,EAAgB,CACvDqF,EAAY,EACZxF,GAAA,MAAAA,EAAgBsD,EAAO,IACvB,OAGFtD,GAAA,MAAAA,EAAgBsD,EAAO,IACvBsC,GAAY,CACd,CAEA7E,EAAM,UAAU,IAAM,CAEhBF,IACFc,GAAS,QAAU,IAAI,KACvBkD,GAAgB,EAAI,EAExB,EAAG,CAAChE,CAAM,CAAC,EAEXE,EAAM,UAAU,IAAM,CAChBG,GAAWA,GAEIkB,EAAU,QAAQ,iBAAiB,GAAG,EAC9C,QAASiE,GAAmB,CACnC,IAAMC,EAAYD,GACdC,EAAU,aAAeA,EAAU,cAAgBA,EAAU,YAAcA,EAAU,cACvFA,EAAU,UAAU,IAAI,iBAAiB,CAE7C,CAAC,CAEL,EAAG,CAACpF,CAAO,CAAC,EAEZ,SAAS2D,GAAgB0B,EAAe,CACtC,IAAMjC,EAAU,SAAS,cAAc,uBAAuB,EAE1D,CAACA,GAAW,CAACxE,IAEbyG,GACFrC,EACE,SAAS,KACT,CACE,WAAY,OACd,EACA,EACF,EAEAA,EAAII,EAAS,CACX,aAAc,GAAGhF,OACjB,SAAU,SACV,UAAW,SAAS8D,GAAS,8DAC7B,gBAAiB,MACjB,mBAAoB,2BACpB,mBAAoB,GAAGqC,EAAY,YACnC,yBAA0B,gBAAgBA,EAAY,KAAK,KAAK,GAAG,IACrE,CAAC,IAGDE,EAAMrB,EAAS,UAAU,EACzBqB,EAAMrB,EAAS,WAAW,EAC1BqB,EAAMrB,EAAS,cAAc,EAC7BJ,EAAII,EAAS,CACX,mBAAoB,2BACpB,mBAAoB,GAAGmB,EAAY,YACnC,yBAA0B,gBAAgBA,EAAY,KAAK,KAAK,GAAG,IACrE,CAAC,GAEL,CAEA,SAASe,GAAmBC,EAAY,CACtC,IAAMC,EAAQD,GAAK,OAAO,WAAalH,GAAuB,OAAO,WAAa,EAC5EwG,EAAIU,EAAI,CAAClH,EAAsB,EAEjCyC,GAAsB,SACxB,OAAO,aAAaA,GAAsB,OAAO,EAGnDkC,EAAI9B,EAAU,QAAS,CACrB,WAAY,aAAaqD,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,KACxF,UAAW,SAASiB,qBAAyBX,SAC/C,CAAC,EAEG,CAACU,GAAKrE,EAAU,UAClBJ,GAAsB,QAAU,WAAW,IAAM,CAC/CkC,EAAI9B,EAAU,QAAS,CACrB,WAAY,OACZ,UAAW,kBAAkB2B,GAAc3B,EAAU,OAAsB,SAC7E,CAAC,CACH,EAAG,GAAG,EAEV,CAEA,SAASuE,GAAarD,EAA2CiB,EAA2B,CAC1F,GAAIA,EAAoB,EAAG,OAC3B,IAAMqC,GAAgB,OAAO,WAAarH,GAAuB,OAAO,WAClEsH,EAAWD,EAAerC,GAAqB,EAAIqC,GACnDE,EAAO,CAACvH,EAAsBgF,EAAoBhF,EAExD2E,EAAI9B,EAAU,QAAS,CACrB,UAAW,SAASyE,qBAA4BC,UAChD,WAAY,MACd,CAAC,CACH,CAEA,SAASC,GAAgBzD,EAA2CmD,EAAY,CAC9E,IAAMC,EAAQD,GAAK,OAAO,WAAalH,GAAuB,OAAO,WAAa,EAC5EwG,EAAIU,EAAI,CAAClH,EAAsB,EAEjCkH,GACFvC,EAAI9B,EAAU,QAAS,CACrB,WAAY,aAAaqD,EAAY,0BAA0BA,EAAY,KAAK,KAAK,GAAG,KACxF,UAAW,SAASiB,qBAAyBX,SAC/C,CAAC,CAEL,CAEA,OACEhF,EAAA,cAAiB,OAAhB,CACC,MAAOL,EACP,aAAe+F,GAAe,CAC5B,GAAI9G,IAAa,OAAW,CAC1BC,GAAA,MAAAA,EAAe6G,GACf,OAGGA,GAGHxF,EAAiB,EAAI,EACrBH,EAAU2F,CAAC,GAHXjB,EAAY,CAKhB,EACA,KAAM3E,GAENE,EAAA,cAACiG,GAAc,SAAd,CACC,MAAO,CACL,QAAA9F,EACA,gBAAAwB,GACA,WAAAzC,EACA,mBAAA0C,GACA,UAAAP,EACA,WAAAV,EACA,gBAAAmD,GACA,aAAAjF,EACA,QAAAyD,GACA,WAAAlC,EACA,UAAA2E,GACA,OAAA9B,GACA,YAAA3D,EACA,OAAAQ,EACA,WAAAiC,GACA,YAAA0C,EACA,aAAAmB,GACA,mBAAAH,GACA,gBAAAO,GACA,eAAA7E,EACA,SAAAvC,EACA,MAAAe,EACA,iBAAA+B,CACF,GAEC5C,CACH,CACF,CAEJ,CAEA,IAAMoH,GAAUlG,EAAM,WACpB,SAAU,CAAE,SAAAlB,EAAU,GAAGqH,CAAK,EAAGC,EAAK,CACpC,GAAM,CAAE,WAAAzF,EAAY,WAAAzB,EAAY,UAAA6F,EAAW,WAAAhD,EAAY,OAAAjC,EAAQ,QAAAK,CAAQ,EAAIkG,GAAiB,EACtFC,EAAcC,GAAgBH,EAAKzF,CAAU,EAC7C6F,EAAgBtH,GAAcA,EAAW,OAAS,EAExD,OACEc,EAAA,cAAiB,UAAhB,CACC,UAAW+E,EACX,IAAKuB,EACL,sBAAqBnG,EAAU,OAAS,QACxC,eAAa,GACb,mBAAkBL,GAAU0G,EAAgB,OAAS,QACrD,2BAA0B1G,GAAUiC,EAAa,OAAS,QACzD,GAAGoE,EACN,CAEJ,CACF,EAEAD,GAAQ,YAAc,iBAMtB,IAAMO,GAAUzG,EAAM,WAAyC,SAC7D,CAAE,SAAAlB,EAAU,gBAAA4H,EAAiB,qBAAAC,EAAsB,eAAAC,EAAgB,MAAAC,EAAO,GAAGV,CAAK,EAClFC,EACA,CACA,GAAM,CACJ,UAAA/E,EACA,QAAAiB,EACA,UAAAyC,EACA,OAAA9B,EACA,YAAA3D,EACA,eAAA6B,EACA,iBAAAO,EACA,QAAAvB,EACA,YAAAsE,EACA,MAAA9E,EACA,SAAAf,EACA,aAAAC,EACA,WAAAuB,CACF,EAAIiG,GAAiB,EACfC,EAAcC,GAAgBH,EAAK/E,CAAS,EAElD,OAAArB,EAAM,UAAU,IAAM,CAEpBI,EAAW,EAAI,CACjB,EAAG,CAAC,CAAC,EAGHJ,EAAA,cAAiB,UAAhB,CACC,gBAAkB8G,GAAM,CAClBJ,EACFA,EAAgBI,CAAC,GAEjBA,EAAE,eAAe,EACjBzF,EAAU,QAAQ,MAAM,EAE5B,EACA,cAAeiB,EACf,qBAAuBwE,GAAM,CAE3B,GADAH,GAAA,MAAAA,EAAuBG,GACnB,CAACnH,EAAO,CACVmH,EAAE,eAAe,EACjB,OAEE3F,EAAe,UACjBA,EAAe,QAAU,IAE3B2F,EAAE,eAAe,EACjBjI,GAAA,MAAAA,EAAe,IACX,GAACS,GAAeV,IAAa,SAIjC6F,EAAY,CACd,EACA,cAAexB,EACf,YAAa8B,EACb,IAAKuB,EACL,MACE5E,GAAoBA,EAAiB,OAAS,EACzC,CACC,sBAAuB,GAAGA,EAAiB,CAAC,MAC5C,GAAGmF,CACL,EACAA,EAEL,GAAGV,EACJ,cAAY,GACZ,sBAAqBhG,EAAU,OAAS,SAEvCrB,CACH,CAEJ,CAAC,EAED2H,GAAQ,YAAc,iBAEtB,SAASM,GAAW,CAAE,SAAAjI,EAAU,OAAAmE,EAAQ,aAAApE,EAAc,GAAGsH,CAAK,EAAgB,CAC5E,GAAM,CAAE,aAAAP,EAAc,mBAAAH,EAAoB,gBAAAO,CAAgB,EAAIK,GAAiB,EAE/E,GAAI,CAACT,EACH,MAAM,IAAI,MAAM,oDAAoD,EAGtE,OACE5F,EAAA,cAACrB,GAAA,CACC,OAAM,GACN,QAAS,IAAM,CACb8G,EAAmB,EAAK,CAC1B,EACA,OAAQ,CAACqB,EAAGE,IAAM,CAChBpB,EAAakB,EAAGE,CAAC,EACjB/D,GAAA,MAAAA,EAAS6D,EAAGE,EACd,EACA,aAAetB,GAAM,CACfA,GACFD,EAAmBC,CAAC,EAEtB7G,GAAA,MAAAA,EAAe6G,EACjB,EACA,UAAWM,EACV,GAAGG,GAEHrH,CACH,CAEJ,CAEO,IAAMmI,GAAS,CACpB,KAAAtI,GACA,WAAAoI,GACA,QAAAN,GACA,QAAAP,GACA,QAAyB,UACzB,OAAwB,SACxB,MAAuB,QACvB,MAAuB,QACvB,YAA6B,aAC/B", "names": ["DialogPrimitive", "React", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useDrawerContext", "styleInject", "css", "insertAt", "head", "style", "styleInject", "useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "chain", "callbacks", "args", "callback", "isMac", "testPlatform", "isIPhone", "isIPad", "testPlatform", "isMac", "isIOS", "isIPhone", "re", "visualViewport", "isScrollable", "node", "style", "getScrollParent", "nonTextInputTypes", "preventScrollCount", "restore", "usePreventScroll", "options", "isDisabled", "useIsomorphicLayoutEffect", "preventScrollMobileSafari", "preventScrollStandard", "chain", "setStyle", "scrollable", "lastY", "onTouchStart", "e", "onTouchMove", "y", "scrollTop", "bottom", "onTouchEnd", "target", "isInput", "onFocus", "scrollIntoView", "onWindowScroll", "scrollX", "scrollY", "restoreStyles", "removeEvents", "addEvent", "element", "value", "cur", "event", "handler", "root", "scrollableTop", "targetTop", "targetBottom", "keyboardHeight", "React", "setRef", "ref", "value", "composeRefs", "refs", "node", "useComposedRefs", "React", "previousBodyPosition", "usePositionFixed", "isOpen", "modal", "nested", "hasBeenOpened", "activeUrl", "setActiveUrl", "scrollPos", "setPositionFixed", "scrollX", "innerHeight", "bottomBarHeight", "restorePositionSetting", "y", "x", "onScroll", "React", "cache", "set", "el", "styles", "ignoreCache", "originalStyles", "key", "value", "cache", "reset", "prop", "getTranslateY", "element", "style", "transform", "mat", "dampenValue", "v", "TRANSITIONS", "VELOCITY_THRESHOLD", "React", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "args", "_a", "useUncontrolledState", "defaultProp", "onChange", "uncontrolledState", "value", "prevValueRef", "handleChange", "useControllableState", "prop", "uncontrolledProp", "setUncontrolledProp", "isControlled", "setValue", "nextValue", "useSnapPoints", "activeSnapPointProp", "setActiveSnapPointProp", "snapPoints", "drawerRef", "overlayRef", "fadeFromIndex", "onSnapPointChange", "activeSnapPoint", "setActiveSnapPoint", "useControllableState", "isLastSnapPoint", "React", "shouldFade", "activeSnapPointIndex", "_a", "snapPoint", "snapPointsOffset", "hasW<PERSON>ow", "isPx", "snapPointAsNumber", "height", "activeSnapPointOffset", "snapToPoint", "newSnapPointIndex", "snapPointHeight", "set", "TRANSITIONS", "newIndex", "onRelease", "draggedDistance", "closeDrawer", "velocity", "currentPosition", "isOverlaySnapPoint", "<PERSON><PERSON><PERSON><PERSON>", "closestSnapPoint", "prev", "curr", "VELOCITY_THRESHOLD", "dragDirection", "onDrag", "newYValue", "getPercentageDragged", "absDraggedDistance", "isDraggingDown", "targetSnapPointIndex", "snapPointDistance", "percentageDragged", "CLOSE_THRESHOLD", "SCROLL_LOCK_TIMEOUT", "BORDER_RADIUS", "NESTED_DISPLACEMENT", "WINDOW_TOP_OFFSET", "DRAG_CLASS", "Root", "openProp", "onOpenChange", "children", "shouldScaleBackground", "onDragProp", "onReleaseProp", "snapPoints", "nested", "closeT<PERSON><PERSON>old", "scrollLockTimeout", "dismissible", "fadeFromIndex", "activeSnapPointProp", "setActiveSnapPointProp", "fixed", "modal", "onClose", "_a", "isOpen", "setIsOpen", "React", "hasBeenOpened", "setHasBeenOpened", "visible", "setVisible", "mounted", "setMounted", "isDragging", "setIsDragging", "justReleased", "setJustReleased", "overlayRef", "openTime", "dragStartTime", "dragEndTime", "lastTimeDragPrevented", "isAllowedToDrag", "nestedOpenChangeTimer", "pointerStartY", "keyboardIsOpen", "previousDiffFromInitial", "drawerRef", "drawerHeightRef", "initialDrawerHeight", "onSnapPointChange", "activeSnapPointIndex", "snapPointsOffset", "activeSnapPoint", "setActiveSnapPoint", "onReleaseSnapPoints", "onDragSnapPoints", "shouldFade", "getSnapPointsPercentageDragged", "useSnapPoints", "usePreventScroll", "restorePositionSetting", "usePositionFixed", "getScale", "onPress", "event", "isIOS", "shouldDrag", "el", "isDraggingDown", "element", "date", "highlightedText", "swipeAmount", "getTranslateY", "onDrag", "draggedDistance", "set", "dampenedDraggedDistance", "dampenValue", "absDraggedDistance", "wrapper", "percentageDragged", "snapPointPercentageDragged", "opacityValue", "scaleValue", "borderRadiusValue", "translateYValue", "scaleBackground", "onVisualViewportChange", "focusedElement", "isInput", "visualViewportHeight", "diffFromInitial", "drawerHeight", "offsetFromTop", "activeSnapPointHeight", "height", "newDrawerHeight", "closeDrawer", "TRANSITIONS", "id", "reset", "resetDrawer", "currentSwipeAmount", "onRelease", "y", "timeTaken", "distMoved", "velocity", "VELOCITY_THRESHOLD", "visibleDrawerHeight", "child", "htmlChild", "open", "onNestedOpenChange", "o", "scale", "onNestedDrag", "initialScale", "newScale", "newY", "onNestedRelease", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Overlay", "rest", "ref", "useDrawerContext", "composedRef", "useComposedRefs", "hasSnapPoints", "Content", "onOpenAutoFocus", "onPointerDownOutside", "onAnimationEnd", "style", "e", "NestedRoot", "p", "Drawer"]}