{"mappings": ";;;;;;;;ACEA;;AAGA,SAASA,yCAAT,CAAiBG,OAAjB,EAA8C;IAC5C,MAAM,CAACC,IAAD,EAAOC,OAAP,CAAA,GAAkBJ,qBAAA,CAA8DM,SAA9D,CAAxB,AAAA;IAEAL,kDAAe,CAAC,IAAM;QACpB,IAAIC,OAAJ,EAAa;YACX,oCAAA;YACAE,OAAO,CAAC;gBAAEG,KAAK,EAAEL,OAAO,CAACM,WAAjB;gBAA8BC,MAAM,EAAEP,OAAO,CAACQ,YAAhBD;aAA/B,CAAP,CAAQ;YAER,MAAME,cAAc,GAAG,IAAIC,cAAJ,CAAoBC,CAAAA,OAAD,GAAa;gBACrD,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,OAAd,CAAL,EACE,OAAA;gBAFmD,CAKrD,wEAFC;gBAGD,QAAA;gBACA,IAAI,CAACA,OAAO,CAACG,MAAb,EACE,OAAA;gBAGF,MAAMC,KAAK,GAAGJ,OAAO,CAAC,CAAD,CAArB,AAAA;gBACA,IAAIN,KAAJ,AAAA;gBACA,IAAIE,MAAJ,AAAA;gBAEA,IAAI,eAAA,IAAmBQ,KAAvB,EAA8B;oBAC5B,MAAMC,eAAe,GAAGD,KAAK,CAAC,eAAD,CAA7B,AAD4B,EAE5B,wCADA;oBAEA,MAAME,UAAU,GAAGL,KAAK,CAACC,OAAN,CAAcG,eAAd,CAAA,GAAiCA,eAAe,CAAC,CAAD,CAAhD,GAAsDA,eAAzE,AAAA;oBACAX,KAAK,GAAGY,UAAU,CAAC,YAAD,CAAlB,CAAAZ;oBACAE,MAAM,GAAGU,UAAU,CAAC,WAAD,CAAnB,CAAAV;iBALF,MAMO;oBACL,kDAAA;oBACA,2DAAA;oBACAF,KAAK,GAAGL,OAAO,CAACM,WAAhB,CAAAD;oBACAE,MAAM,GAAGP,OAAO,CAACQ,YAAjB,CAAAD;iBACD;gBAEDL,OAAO,CAAC;oBAzChB,OAyCkBG,KAAF;oBAzChB,QAyCyBE,MAAAA;iBAAV,CAAP,CAAQ;aA5Ba,CAAvB,AA6BC;YAEDE,cAAc,CAACS,OAAf,CAAuBlB,OAAvB,EAAgC;gBAAEmB,GAAG,EAAE,YAALA;aAAlC,CAAgC,CAAA;YAEhC,OAAO,IAAMV,cAAc,CAACW,SAAf,CAAyBpB,OAAzB,CAAb;YAAA,CAAA;SArCF,MAuCE,wEAAA;QACA,wCAAA;QACAE,OAAO,CAACE,SAAD,CAAP,CAAAF;KA1CW,EA4CZ;QAACF,OAAD;KA5CY,CAAf,CA4CC;IAED,OAAOC,IAAP,CAAA;CACD;;ADvDD", "sources": ["packages/react/use-size/src/index.ts", "packages/react/use-size/src/useSize.tsx"], "sourcesContent": ["export { useSize } from './useSize';\n", "/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n"], "names": ["useSize", "React", "useLayoutEffect", "element", "size", "setSize", "useState", "undefined", "width", "offsetWidth", "height", "offsetHeight", "resizeObserver", "ResizeObserver", "entries", "Array", "isArray", "length", "entry", "borderSizeEntry", "borderSize", "observe", "box", "unobserve"], "version": 3, "file": "index.js.map"}