from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, UploadFile, File
from fastapi.security import <PERSON>Auth<PERSON><PERSON><PERSON><PERSON><PERSON>ear<PERSON>, OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import Optional
from datetime import datetime, timedelta
import jwt
from passlib.context import CryptContext
from auth import get_current_user
from fastapi.middleware.cors import CORSMiddleware
import boto3
from botocore.exceptions import ClientError
import os
from database import get_db

app = FastAPI()

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # 允许前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# JWT 配置
SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密码哈希
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# 数据库连接
import sqlite3



class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    username: str
    role: str

class UserInDB(User):
    hashed_password: str

def verify_password(plain_password: str, hashed_password: str):
    return pwd_context.verify(plain_password, hashed_password)

def get_user(db, username: str):
    cursor = db.cursor()
    cursor.execute("SELECT username, hashed_password, role FROM users WHERE username = ?", (username,))
    user_data = cursor.fetchone()
    if user_data:
        return UserInDB(username=user_data[0], hashed_password=user_data[1], role=user_data[2])
    return None

def authenticate_user(db, username: str, password: str):
    user = get_user(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

@app.post("/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    db = get_db()
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_user)):
    return current_user

# 配置腾讯云 COS
COS_ENDPOINT = "https://s3.pek3a.qsstor.com"
COS_BUCKET = "nsfc-medical-docs"
COS_SECRET_ID = "GXVYDNKDQWZZPDBPUEOY"
COS_SECRET_KEY = "B8TaJ3adxpZV8FHh6ePTbYEukOp9zJm1gdkPylmp"

# 初始化 COS 客户端
cos_client = boto3.client(
    "s3",
    endpoint_url=COS_ENDPOINT,
    aws_access_key_id=COS_SECRET_ID,
    aws_secret_access_key=COS_SECRET_KEY,
)

@app.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    try:
        # 验证文件对象
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名无效")

        # 读取文件内容
        file_content = await file.read()

        # 验证文件内容
        if not file_content:
            raise HTTPException(status_code=400, detail="文件内容为空")

        # 规范化文件路径
        file_key = f"uploads/{current_user.username}/{file.filename.replace(' ', '_')}"

        # 设置 ContentType（默认值：application/octet-stream）
        content_type = file.content_type or "application/octet-stream"

        # 使用 put_object 方法上传到 COS
        cos_client.put_object(
            Bucket=COS_BUCKET,
            Key=file_key,
            Body=file_content,
            ContentType=content_type
        )

        return {"message": "文件上传成功", "file_key": file_key}

    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_message = e.response['Error']['Message']
        print(f"COS 错误详情: {error_code} - {error_message}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {error_message}")
    except Exception as e:
        print(f"服务器错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except jwt.PyJWTError:
        raise credentials_exception
    db = get_db()
    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user