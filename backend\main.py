from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, UploadFile, File
from fastapi.security import <PERSON>Auth<PERSON><PERSON><PERSON><PERSON><PERSON>ear<PERSON>, OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import Optional
from datetime import datetime, timedelta
import jwt
from passlib.context import CryptContext
from auth import get_current_user
from fastapi.middleware.cors import CORSMiddleware
import boto3
from botocore.exceptions import ClientError
import os
from database import get_db

app = FastAPI()

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # 允许前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# JWT 配置
SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密码哈希
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# 数据库连接
import sqlite3



class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class User(BaseModel):
    username: str
    role: str

class UserInDB(User):
    hashed_password: str

def verify_password(plain_password: str, hashed_password: str):
    return pwd_context.verify(plain_password, hashed_password)

def get_user(db, username: str):
    cursor = db.cursor()
    cursor.execute("SELECT username, hashed_password, role FROM users WHERE username = ?", (username,))
    user_data = cursor.fetchone()
    if user_data:
        return UserInDB(username=user_data[0], hashed_password=user_data[1], role=user_data[2])
    return None

def authenticate_user(db, username: str, password: str):
    user = get_user(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

@app.post("/login", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    db = get_db()
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_user)):
    return current_user

# 配置 QingStor SDK
from qingstor.sdk.service.qingstor import QingStor
from qingstor.sdk.config import Config

QINGSTOR_CONFIG = Config(
    access_key_id="GXVYDNKDQWZZPDBPUEOY",
    secret_access_key="B8TaJ3adxpZV8FHh6ePTbYEukOp9zJm1gdkPylmp",
)

# 初始化 QingStor 客户端
try:
    qingstor = QingStor(QINGSTOR_CONFIG)
    bucket = qingstor.Bucket("nsfc-medical-docs", "pek3b")
    print("QingStor 客户端初始化成功")
except Exception as e:
    print(f"QingStor 客户端初始化失败: {e}")
    raise

@app.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    try:
        # 验证文件对象
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名无效")

        # 读取文件内容
        file_content = await file.read()

        # 验证文件内容
        if not file_content:
            raise HTTPException(status_code=400, detail="文件内容为空")

        # 规范化文件路径
        from datetime import datetime
        file_ext = file.filename.split(".")[-1] if "." in file.filename else ""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_filename = f"{timestamp}.{file_ext}" if file_ext else timestamp
        file_key = f"uploads/{current_user.username}/{safe_filename}"

        # 设置 Content-Type
        content_type = file.content_type or "application/octet-stream"
        print(f"准备上传文件: {file_key}, 类型: {content_type}, 大小: {len(file_content)}字节")

        # 使用 QingStor SDK 上传 (官方推荐方式)
        try:
            # 重置文件指针
            await file.seek(0)
            
            # 分块上传（适合大文件）
            with file.file as f:
                resp = bucket.put_object(
                    file_key,
                    body=f,
                    content_type=content_type,
                    content_length=str(len(file_content))
                )
                
            if resp.status_code != 201:
                error_detail = resp.content.decode('utf-8') if resp.content else "未知错误"
                print(f"上传失败: {resp.status_code}, 详情: {error_detail}")
                raise HTTPException(
                    status_code=resp.status_code,
                    detail=f"文件上传失败: {error_detail}"
                )
                
            print(f"上传成功: {file_key}")
            return {
                "message": "文件上传成功",
                "file_key": file_key,
                "original_filename": file.filename,
                "safe_filename": safe_filename
            }
            
        except Exception as e:
            print(f"上传异常: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"服务器处理错误: {str(e)}"
            )
            
    except Exception as e:
        print(f"服务器错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")


def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except jwt.PyJWTError:
        raise credentials_exception
    db = get_db()
    user = get_user(db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

# 分析结果API
class AnalysisResult(BaseModel):
    projectName: str
    applicant: str
    phone: str
    story: str
    keywords: list[str]
    extendedSpace: list[str]
    knowledgePoints: list[str]
    macroAndMicro: str

@app.get("/analysis", response_model=AnalysisResult)
async def get_analysis_result():
    db = get_db()
    cursor = db.cursor()
    cursor.execute("SELECT * FROM analysis_results LIMIT 1")
    result = cursor.fetchone()
    if not result:
        raise HTTPException(status_code=404, detail="未找到分析结果")
    return AnalysisResult(
        projectName=result[0],
        applicant=result[1],
        phone=result[2],
        story=result[3],
        keywords=result[4].split(",") if result[4] else [],
        extendedSpace=result[5].split(",") if result[5] else [],
        knowledgePoints=result[6].split(",") if result[6] else [],
        macroAndMicro=result[7]
    )