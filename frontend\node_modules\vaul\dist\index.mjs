"use client"
"use client";import*as x from"@radix-ui/react-dialog";import d from"react";import Ae from"react";var ge=Ae.createContext({drawerRef:{current:null},overlayRef:{current:null},scaleBackground:()=>{},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,isOpen:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},visible:!1,closeDrawer:()=>{},setVisible:()=>{}}),ie=()=>Ae.useContext(ge);function be(e,{insertAt:n}={}){if(!e||typeof document=="undefined")return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",n==="top"&&t.firstChild?t.insertBefore(r,t.firstChild):t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}be(`[vaul-drawer]{touch-action:none;transform:translate3d(0,100%,0);transition:transform .5s cubic-bezier(.32,.72,0,1)}.vaul-dragging .vaul-scrollable{overflow-y:hidden!important}[vaul-drawer][vaul-drawer-visible=true]{transform:translate3d(0,var(--snap-point-height, 0),0)}[vaul-overlay]{opacity:0;transition:opacity .5s cubic-bezier(.32,.72,0,1)}[vaul-overlay][vaul-drawer-visible=true]{opacity:1}[vaul-drawer]:after{content:"";position:absolute;top:100%;background:inherit;background-color:inherit;left:0;right:0;height:200%}[vaul-overlay][vaul-snap-points=true]:not([vaul-snap-points-overlay="true"]):not([data-state="closed"]){opacity:0}[vaul-overlay][vaul-snap-points-overlay=true]:not([vaul-drawer-visible="false"]){opacity:1}@keyframes fake-animation{}@media (hover: hover) and (pointer: fine){[vaul-drawer]{user-select:none}}
`);import{useEffect as ct,useLayoutEffect as ft}from"react";var dt=typeof window!="undefined"?ft:ct;function we(...e){return(...n)=>{for(let t of e)typeof t=="function"&&t(...n)}}function mt(){return Te(/^Mac/)}function pt(){return Te(/^iPhone/)}function gt(){return Te(/^iPad/)||mt()&&navigator.maxTouchPoints>1}function Ee(){return pt()||gt()}function Te(e){return typeof window!="undefined"&&window.navigator!=null?e.test(window.navigator.platform):void 0}var ve=typeof document!="undefined"&&window.visualViewport;function $e(e){let n=window.getComputedStyle(e);return/(auto|scroll)/.test(n.overflow+n.overflowX+n.overflowY)}function Ne(e){for($e(e)&&(e=e.parentElement);e&&!$e(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}var bt=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),ae=0,he;function Pe(e={}){let{isDisabled:n}=e;dt(()=>{if(!n)return ae++,ae===1&&(Ee()?he=ht():he=vt()),()=>{ae--,ae===0&&he()}},[n])}function vt(){return we(le(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),le(document.documentElement,"overflow","hidden"))}function ht(){let e,n=0,t=f=>{e=Ne(f.target),!(e===document.documentElement&&e===document.body)&&(n=f.changedTouches[0].pageY)},r=f=>{if(!e||e===document.documentElement||e===document.body){f.preventDefault();return}let o=f.changedTouches[0].pageY,C=e.scrollTop,L=e.scrollHeight-e.clientHeight;L!==0&&((C<=0&&o>n||C>=L&&o<n)&&f.preventDefault(),n=o)},l=f=>{let o=f.target;Q(o)&&o!==document.activeElement&&(f.preventDefault(),o.style.transform="translateY(-2000px)",o.focus(),requestAnimationFrame(()=>{o.style.transform=""}))},i=f=>{let o=f.target;Q(o)&&(o.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{o.style.transform="",ve&&(ve.height<window.innerHeight?requestAnimationFrame(()=>{Ie(o)}):ve.addEventListener("resize",()=>Ie(o),{once:!0}))}))},c=()=>{window.scrollTo(0,0)},g=window.pageXOffset,T=window.pageYOffset,w=we(le(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),le(document.documentElement,"overflow","hidden"));window.scrollTo(0,0);let S=we(J(document,"touchstart",t,{passive:!1,capture:!0}),J(document,"touchmove",r,{passive:!1,capture:!0}),J(document,"touchend",l,{passive:!1,capture:!0}),J(document,"focus",i,!0),J(window,"scroll",c));return()=>{w(),S(),window.scrollTo(g,T)}}function le(e,n,t){let r=e.style[n];return e.style[n]=t,()=>{e.style[n]=r}}function J(e,n,t,r){return e.addEventListener(n,t,r),()=>{e.removeEventListener(n,t,r)}}function Ie(e){let n=document.scrollingElement||document.documentElement;for(;e&&e!==n;){let t=Ne(e);if(t!==document.documentElement&&t!==document.body&&t!==e){let r=t.getBoundingClientRect().top,l=e.getBoundingClientRect().top,i=e.getBoundingClientRect().bottom,c=t.getBoundingClientRect().bottom;i>c&&(t.scrollTop+=l-r)}e=t.parentElement}}function Q(e){return e instanceof HTMLInputElement&&!bt.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}import*as Fe from"react";function wt(e,n){typeof e=="function"?e(n):e!=null&&(e.current=n)}function Et(...e){return n=>e.forEach(t=>wt(t,n))}function ye(...e){return Fe.useCallback(Et(...e),e)}import se from"react";var F=null;function Ve({isOpen:e,modal:n,nested:t,hasBeenOpened:r}){let[l,i]=se.useState(typeof window!="undefined"?window.location.href:""),c=se.useRef(0);function g(){if(F===null&&e){F={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height};let{scrollX:w,innerHeight:S}=window;document.body.style.setProperty("position","fixed","important"),document.body.style.top=`${-c.current}px`,document.body.style.left=`${-w}px`,document.body.style.right="0px",document.body.style.height="auto",setTimeout(()=>requestAnimationFrame(()=>{let f=S-window.innerHeight;f&&c.current>=S&&(document.body.style.top=`${-(c.current+f)}px`)}),300)}}function T(){if(F!==null){let w=-parseInt(document.body.style.top,10),S=-parseInt(document.body.style.left,10);document.body.style.position=F.position,document.body.style.top=F.top,document.body.style.left=F.left,document.body.style.height=F.height,document.body.style.right="unset",requestAnimationFrame(()=>{if(l!==window.location.href){i(window.location.href);return}window.scrollTo(S,w)}),F=null}}return se.useEffect(()=>{function w(){c.current=window.scrollY}return w(),window.addEventListener("scroll",w),()=>{window.removeEventListener("scroll",w)}},[]),se.useEffect(()=>{t||!r||(e?(g(),n||setTimeout(()=>{T()},500)):T())},[e,r,l]),{restorePositionSetting:T}}import _ from"react";var We=new WeakMap;function h(e,n,t=!1){if(!e||!(e instanceof HTMLElement)||!n)return;let r={};Object.entries(n).forEach(([l,i])=>{if(l.startsWith("--")){e.style.setProperty(l,i);return}r[l]=e.style[l],e.style[l]=i}),!t&&We.set(e,r)}function Z(e,n){if(!e||!(e instanceof HTMLElement))return;let t=We.get(e);t&&(n?e.style[n]=t[n]:Object.entries(t).forEach(([r,l])=>{e.style[r]=l}))}function ee(e){let n=window.getComputedStyle(e),t=n.transform||n.webkitTransform||n.mozTransform,r=t.match(/^matrix3d\((.+)\)$/);return r?parseFloat(r[1].split(", ")[13]):(r=t.match(/^matrix\((.+)\)$/),r?parseFloat(r[1].split(", ")[5]):null)}function Ue(e){return 8*(Math.log(e+1)-2)}var v={DURATION:.5,EASE:[.32,.72,0,1]},ue=.4;import U from"react";function je(e){let n=U.useRef(e);return U.useEffect(()=>{n.current=e}),U.useMemo(()=>(...t)=>{var r;return(r=n.current)==null?void 0:r.call(n,...t)},[])}function Tt({defaultProp:e,onChange:n}){let t=U.useState(e),[r]=t,l=U.useRef(r),i=je(n);return U.useEffect(()=>{l.current!==r&&(i(r),l.current=r)},[r,l,i]),t}function Ye({prop:e,defaultProp:n,onChange:t=()=>{}}){let[r,l]=Tt({defaultProp:n,onChange:t}),i=e!==void 0,c=i?e:r,g=je(t),T=U.useCallback(w=>{if(i){let f=typeof w=="function"?w(e):w;f!==e&&g(f)}else l(w)},[i,e,l,g]);return[c,T]}function ke({activeSnapPointProp:e,setActiveSnapPointProp:n,snapPoints:t,drawerRef:r,overlayRef:l,fadeFromIndex:i,onSnapPointChange:c}){let[g,T]=Ye({prop:e,defaultProp:t==null?void 0:t[0],onChange:n}),w=_.useMemo(()=>g===(t==null?void 0:t[t.length-1]),[t,g]),S=t&&t.length>0&&i&&t[i]===g||!t,f=_.useMemo(()=>{var p;return(p=t==null?void 0:t.findIndex(b=>b===g))!=null?p:null},[t,g]),o=_.useMemo(()=>{var p;return(p=t==null?void 0:t.map(b=>{let E=typeof window!="undefined",y=typeof b=="string",O=0;y&&(O=parseInt(b,10));let P=y?O:E?b*window.innerHeight:0;return E?window.innerHeight-P:P}))!=null?p:[]},[t]),C=_.useMemo(()=>f!==null?o==null?void 0:o[f]:null,[o,f]),L=_.useCallback(p=>{var E;let b=(E=o==null?void 0:o.findIndex(y=>y===p))!=null?E:null;c(b),h(r.current,{transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,transform:`translate3d(0, ${p}px, 0)`}),o&&b!==o.length-1&&b!==i?h(l.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,opacity:"0"}):h(l.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,opacity:"1"}),T(b!==null?t==null?void 0:t[b]:null)},[r.current,t,o,i,l,T]);_.useEffect(()=>{var p;if(e){let b=(p=t==null?void 0:t.findIndex(E=>E===e))!=null?p:null;o&&b&&typeof o[b]=="number"&&L(o[b])}},[e,t,o,L]);function N({draggedDistance:p,closeDrawer:b,velocity:E}){if(i===void 0)return;let y=C-p,O=f===i-1,P=f===0;if(O&&h(l.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),E>2&&p<0){b();return}if(E>2&&p>0&&o&&t){L(o[t.length-1]);return}let Y=o==null?void 0:o.reduce((A,k)=>typeof A!="number"||typeof k!="number"?A:Math.abs(k-y)<Math.abs(A-y)?k:A);if(E>ue&&Math.abs(p)<window.innerHeight*.4){let A=p>0?1:-1;if(A>0&&w){L(o[t.length-1]);return}if(P&&A<0&&b(),f===null)return;L(o[f+A]);return}L(Y)}function V({draggedDistance:p}){if(C===null)return;let b=C-p;h(r.current,{transform:`translate3d(0, ${b}px, 0)`})}function H(p,b){if(!t||typeof f!="number"||!o||i===void 0)return null;let E=f===i-1;if(f>=i&&b)return 0;if(E&&!b)return 1;if(!S&&!E)return null;let O=E?f+1:f-1,P=E?o[O]-o[O-1]:o[O+1]-o[O],Y=p/Math.abs(P);return E?1-Y:Y}return{isLastSnapPoint:w,activeSnapPoint:g,shouldFade:S,getPercentageDragged:H,setActiveSnapPoint:T,activeSnapPointIndex:f,onRelease:N,onDrag:V,snapPointsOffset:o}}var yt=.25,Dt=100,ze=8,j=16,Be=26,_e="vaul-dragging";function qe({open:e,onOpenChange:n,children:t,shouldScaleBackground:r,onDrag:l,onRelease:i,snapPoints:c,nested:g,closeThreshold:T=yt,scrollLockTimeout:w=Dt,dismissible:S=!0,fadeFromIndex:f=c&&c.length-1,activeSnapPoint:o,setActiveSnapPoint:C,fixed:L,modal:N=!0,onClose:V}){var Oe;let[H=!1,p]=d.useState(!1),[b,E]=d.useState(!1),[y,O]=d.useState(!1),[P,Y]=d.useState(!1),[A,k]=d.useState(!1),[Ge,De]=d.useState(!1),W=d.useRef(null),te=d.useRef(null),ce=d.useRef(null),Se=d.useRef(null),q=d.useRef(null),K=d.useRef(!1),fe=d.useRef(null),de=d.useRef(0),z=d.useRef(!1),Re=d.useRef(0),u=d.useRef(null),xe=d.useRef(((Oe=u.current)==null?void 0:Oe.getBoundingClientRect().height)||0),me=d.useRef(0),Je=d.useCallback(a=>{c&&a===G.length-1&&(te.current=new Date)},[]),{activeSnapPoint:Qe,activeSnapPointIndex:X,setActiveSnapPoint:He,onRelease:Ze,snapPointsOffset:G,onDrag:et,shouldFade:Me,getPercentageDragged:tt}=ke({snapPoints:c,activeSnapPointProp:o,setActiveSnapPointProp:C,drawerRef:u,fadeFromIndex:f,overlayRef:W,onSnapPointChange:Je});Pe({isDisabled:!H||A||!N||Ge||!b});let{restorePositionSetting:nt}=Ve({isOpen:H,modal:N,nested:g,hasBeenOpened:b});function ne(){return(window.innerWidth-Be)/window.innerWidth}function rt(a){var s;!S&&!c||u.current&&!u.current.contains(a.target)||(xe.current=((s=u.current)==null?void 0:s.getBoundingClientRect().height)||0,k(!0),ce.current=new Date,Ee()&&window.addEventListener("touchend",()=>K.current=!1,{once:!0}),a.target.setPointerCapture(a.pointerId),de.current=a.screenY)}function Le(a,s){var $;let m=a,M=new Date,R=($=window.getSelection())==null?void 0:$.toString(),D=u.current?ee(u.current):null;if(te.current&&M.getTime()-te.current.getTime()<500)return!1;if(D>0)return!0;if(R&&R.length>0)return!1;if(q.current&&M.getTime()-q.current.getTime()<w&&D===0)return q.current=new Date,!1;if(s)return q.current=new Date,!1;for(;m;){if(m.scrollHeight>m.clientHeight){if(m.scrollTop!==0)return q.current=new Date,!1;if(m.getAttribute("role")==="dialog")return!0}m=m.parentNode}return!0}function ot(a){if(A){let s=de.current-a.screenY,m=s>0;if(c&&X===0&&!S||!K.current&&!Le(a.target,m))return;if(u.current.classList.add(_e),K.current=!0,h(u.current,{transition:"none"}),h(W.current,{transition:"none"}),c&&et({draggedDistance:s}),s>0&&!c){let I=Ue(s);h(u.current,{transform:`translate3d(0, ${Math.min(I*-1,0)}px, 0)`});return}let M=Math.abs(s),R=document.querySelector("[vaul-drawer-wrapper]"),D=M/xe.current,$=tt(M,m);$!==null&&(D=$);let pe=1-D;if((Me||f&&X===f-1)&&(l==null||l(a,D),h(W.current,{opacity:`${pe}`,transition:"none"},!0)),R&&W.current&&r){let I=Math.min(ne()+D*(1-ne()),1),oe=8-D*8,ut=Math.max(0,14-D*14);h(R,{borderRadius:`${oe}px`,transform:`scale(${I}) translate3d(0, ${ut}px, 0)`,transition:"none"},!0)}c||h(u.current,{transform:`translate3d(0, ${M}px, 0)`})}}d.useEffect(()=>()=>{re(!1),nt()},[]),d.useEffect(()=>{var s;function a(){var M;if(!u.current)return;let m=document.activeElement;if(Q(m)||z.current){let R=((M=window.visualViewport)==null?void 0:M.height)||0,D=window.innerHeight-R,$=u.current.getBoundingClientRect().height||0;me.current||(me.current=$);let pe=u.current.getBoundingClientRect().top;if(Math.abs(Re.current-D)>60&&(z.current=!z.current),c&&c.length>0&&G&&X){let I=G[X]||0;D+=I}if(Re.current=D,$>R||z.current){let I=u.current.getBoundingClientRect().height,oe=I;I>R&&(oe=R-Be),L?u.current.style.height=`${I-Math.max(D,0)}px`:u.current.style.height=`${Math.max(oe,R-pe)}px`}else u.current.style.height=`${me.current}px`;c&&c.length>0&&!z.current?u.current.style.bottom="0px":u.current.style.bottom=`${Math.max(D,0)}px`}}return(s=window.visualViewport)==null||s.addEventListener("resize",a),()=>{var m;return(m=window.visualViewport)==null?void 0:m.removeEventListener("resize",a)}},[X,c,G]);function B(){u.current&&(V==null||V(),u.current&&(h(u.current,{transform:"translate3d(0, 100%, 0)",transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),h(W.current,{opacity:"0",transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),re(!1)),setTimeout(()=>{O(!1),p(!1)},300),setTimeout(()=>{c&&He(c[0])},500))}d.useEffect(()=>{if(!H&&r){let a=setTimeout(()=>{Z(document.body)},200);return()=>clearTimeout(a)}},[H,r]),d.useEffect(()=>{e?(p(!0),E(!0)):B()},[e]),d.useEffect(()=>{P&&(n==null||n(H))},[H]),d.useEffect(()=>{Y(!0)},[]);function Ce(){if(!u.current)return;let a=document.querySelector("[vaul-drawer-wrapper]"),s=ee(u.current);h(u.current,{transform:"translate3d(0, 0, 0)",transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`}),h(W.current,{transition:`opacity ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,opacity:"1"}),r&&s&&s>0&&H&&h(a,{borderRadius:`${ze}px`,overflow:"hidden",transform:`scale(${ne()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,transformOrigin:"top",transitionProperty:"transform, border-radius",transitionDuration:`${v.DURATION}s`,transitionTimingFunction:`cubic-bezier(${v.EASE.join(",")})`},!0)}function it(a){if(!A||!u.current)return;K.current&&Q(a.target)&&a.target.blur(),u.current.classList.remove(_e),K.current=!1,k(!1),Se.current=new Date;let s=ee(u.current);if(!Le(a.target,!1)||!s||Number.isNaN(s)||ce.current===null)return;let m=a.screenY,M=Se.current.getTime()-ce.current.getTime(),R=de.current-m,D=Math.abs(R)/M;if(D>.05&&(De(!0),setTimeout(()=>{De(!1)},200)),c){Ze({draggedDistance:R,closeDrawer:B,velocity:D});return}if(R>0){Ce(),i==null||i(a,!0);return}if(D>ue){B(),i==null||i(a,!1);return}let $=Math.min(u.current.getBoundingClientRect().height||0,window.innerHeight);if(s>=$*T){B(),i==null||i(a,!1);return}i==null||i(a,!0),Ce()}d.useEffect(()=>{H&&(te.current=new Date,re(!0))},[H]),d.useEffect(()=>{y&&y&&u.current.querySelectorAll("*").forEach(s=>{let m=s;(m.scrollHeight>m.clientHeight||m.scrollWidth>m.clientWidth)&&m.classList.add("vaul-scrollable")})},[y]);function re(a){let s=document.querySelector("[vaul-drawer-wrapper]");!s||!r||(a?(h(document.body,{background:"black"},!0),h(s,{borderRadius:`${ze}px`,overflow:"hidden",transform:`scale(${ne()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,transformOrigin:"top",transitionProperty:"transform, border-radius",transitionDuration:`${v.DURATION}s`,transitionTimingFunction:`cubic-bezier(${v.EASE.join(",")})`})):(Z(s,"overflow"),Z(s,"transform"),Z(s,"borderRadius"),h(s,{transitionProperty:"transform, border-radius",transitionDuration:`${v.DURATION}s`,transitionTimingFunction:`cubic-bezier(${v.EASE.join(",")})`})))}function at(a){let s=a?(window.innerWidth-j)/window.innerWidth:1,m=a?-j:0;fe.current&&window.clearTimeout(fe.current),h(u.current,{transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,transform:`scale(${s}) translate3d(0, ${m}px, 0)`}),!a&&u.current&&(fe.current=setTimeout(()=>{h(u.current,{transition:"none",transform:`translate3d(0, ${ee(u.current)}px, 0)`})},500))}function lt(a,s){if(s<0)return;let m=(window.innerWidth-j)/window.innerWidth,M=m+s*(1-m),R=-j+s*j;h(u.current,{transform:`scale(${M}) translate3d(0, ${R}px, 0)`,transition:"none"})}function st(a,s){let m=s?(window.innerWidth-j)/window.innerWidth:1,M=s?-j:0;s&&h(u.current,{transition:`transform ${v.DURATION}s cubic-bezier(${v.EASE.join(",")})`,transform:`scale(${m}) translate3d(0, ${M}px, 0)`})}return d.createElement(x.Root,{modal:N,onOpenChange:a=>{if(e!==void 0){n==null||n(a);return}a?(E(!0),p(a)):B()},open:H},d.createElement(ge.Provider,{value:{visible:y,activeSnapPoint:Qe,snapPoints:c,setActiveSnapPoint:He,drawerRef:u,overlayRef:W,scaleBackground:re,onOpenChange:n,onPress:rt,setVisible:O,onRelease:it,onDrag:ot,dismissible:S,isOpen:H,shouldFade:Me,closeDrawer:B,onNestedDrag:lt,onNestedOpenChange:at,onNestedRelease:st,keyboardIsOpen:z,openProp:e,modal:N,snapPointsOffset:G}},t))}var Ke=d.forwardRef(function({children:e,...n},t){let{overlayRef:r,snapPoints:l,onRelease:i,shouldFade:c,isOpen:g,visible:T}=ie(),w=ye(t,r),S=l&&l.length>0;return d.createElement(x.Overlay,{onMouseUp:i,ref:w,"vaul-drawer-visible":T?"true":"false","vaul-overlay":"","vaul-snap-points":g&&S?"true":"false","vaul-snap-points-overlay":g&&c?"true":"false",...n})});Ke.displayName="Drawer.Overlay";var Xe=d.forwardRef(function({children:e,onOpenAutoFocus:n,onPointerDownOutside:t,onAnimationEnd:r,style:l,...i},c){let{drawerRef:g,onPress:T,onRelease:w,onDrag:S,dismissible:f,keyboardIsOpen:o,snapPointsOffset:C,visible:L,closeDrawer:N,modal:V,openProp:H,onOpenChange:p,setVisible:b}=ie(),E=ye(c,g);return d.useEffect(()=>{b(!0)},[]),d.createElement(x.Content,{onOpenAutoFocus:y=>{n?n(y):(y.preventDefault(),g.current.focus())},onPointerDown:T,onPointerDownOutside:y=>{if(t==null||t(y),!V){y.preventDefault();return}o.current&&(o.current=!1),y.preventDefault(),p==null||p(!1),!(!f||H!==void 0)&&N()},onPointerMove:S,onPointerUp:w,ref:E,style:C&&C.length>0?{"--snap-point-height":`${C[0]}px`,...l}:l,...i,"vaul-drawer":"","vaul-drawer-visible":L?"true":"false"},e)});Xe.displayName="Drawer.Content";function St({children:e,onDrag:n,onOpenChange:t,...r}){let{onNestedDrag:l,onNestedOpenChange:i,onNestedRelease:c}=ie();if(!l)throw new Error("Drawer.NestedRoot must be placed in another drawer");return d.createElement(qe,{nested:!0,onClose:()=>{i(!1)},onDrag:(g,T)=>{l(g,T),n==null||n(g,T)},onOpenChange:g=>{g&&i(g),t==null||t(g)},onRelease:c,...r},e)}var Qt={Root:qe,NestedRoot:St,Content:Xe,Overlay:Ke,Trigger:x.Trigger,Portal:x.Portal,Close:x.Close,Title:x.Title,Description:x.Description};export{Qt as Drawer};
//# sourceMappingURL=index.mjs.map