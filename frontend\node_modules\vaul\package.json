{"name": "vaul", "version": "0.7.9", "description": "Drawer component for React.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup src/index.tsx", "dev": "tsup src/index.tsx --watch", "dev:website": "turbo run dev --filter=website...", "dev:test": "turbo run dev --filter=test...", "format": "prettier --write .", "test": "playwright test"}, "keywords": ["react", "drawer", "dialog", "modal"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://vaul.emilkowal.ski/", "repository": {"type": "git", "url": "https://github.com/emilkowalski/vaul.git"}, "bugs": {"url": "https://github.com/emilkowalski/vaul/issues"}, "devDependencies": {"@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "tsup": "^6.4.0", "turbo": "1.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "packageManager": "pnpm@6.32.11", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}}