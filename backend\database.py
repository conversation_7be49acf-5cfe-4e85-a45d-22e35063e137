import sqlite3
from passlib.context import CryptContext

# 密码哈希配置
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_db():
    conn = sqlite3.connect("database.db")
    return conn

def init_db():
    conn = sqlite3.connect("database.db")
    cursor = conn.cursor()
    
    # 创建 users 表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            hashed_password TEXT NOT NULL,
            role TEXT NOT NULL
        )
    """)
    
    # 插入初始用户数据
    users = [
        ("admin", pwd_context.hash("admin"), "admin"),
        ("user", pwd_context.hash("user"), "user")
    ]
    
    cursor.executemany("""
        INSERT OR IGNORE INTO users (username, hashed_password, role)
        VALUES (?, ?, ?)
    """, users)
    
    conn.commit()
    conn.close()

# 初始化数据库
init_db()