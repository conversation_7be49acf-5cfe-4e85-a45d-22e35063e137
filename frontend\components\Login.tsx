import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { 
  Stethoscope, 
  Heart, 
  Activity, 
  Shield, 
  User, 
  Lock,
  Eye,
  EyeOff
} from 'lucide-react';

interface LoginProps {
  onLogin: (userData: { username: string; role: string }) => void;
}

export function Login({ onLogin }: LoginProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('http://localhost:8000/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username,
          password,
        }),
      });

      if (!response.ok) {
        throw new Error('用户名或密码错误');
      }

      const data = await response.json();
      localStorage.setItem('access_token', data.access_token);
      
      // 解析 JWT 中的用户信息
      const tokenParts = data.access_token.split('.');
      const payload = JSON.parse(atob(tokenParts[1]));
      const { sub, role, exp } = payload;
      
      // 根据角色跳转页面
      if (role === 'admin') {
        window.location.href = '/admin';
      } else {
        window.location.href = '/dashboard';
      }
      
      onLogin({ username, role });
    } catch (error) {
      console.error('登录失败:', error);
      alert('登录失败，请检查用户名和密码');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-100 dark:bg-blue-900/10 rounded-full opacity-20"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-green-100 dark:bg-green-900/10 rounded-full opacity-20"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-red-100 dark:bg-red-900/10 rounded-full opacity-20"></div>
        <div className="absolute bottom-40 right-1/3 w-16 h-16 bg-purple-100 dark:bg-purple-900/10 rounded-full opacity-20"></div>
      </div>

      <div className="w-full max-w-md relative z-10">
        {/* 医学主题头部 */}
        <div className="text-center space-y-4 mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/20">
              <Stethoscope className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-3xl font-bold text-primary">医疗管理系统</h1>
            <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/20">
              <Heart className="h-8 w-8 text-red-500" />
            </div>
          </div>
          <p className="text-muted-foreground">
            请登录您的账户以访问医疗文档管理系统
          </p>
          <Activity className="h-6 w-6 text-green-500 animate-pulse mx-auto" />
        </div>

        {/* 登录表单 */}
        <Card className="border-2 shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400">
              <Shield className="h-5 w-5" />
              安全登录
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="username" className="flex items-center gap-2">
                  <User className="h-4 w-4 text-blue-600" />
                  用户名
                </Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="请输入用户名"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="h-12"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-blue-600" />
                  密码
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="请输入密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-12 pr-12"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full h-12 text-base"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 animate-spin" />
                    登录中...
                  </div>
                ) : (
                  "登录"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* 演示账户信息 */}
        <Card className="mt-6 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-950/20 dark:to-green-950/20 border-blue-200 dark:border-blue-800">
          <CardContent className="p-4">
            <div className="text-center space-y-2">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                演示账户
              </h4>
              <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <p>管理员：用户名 <span className="font-mono bg-white dark:bg-gray-800 px-2 py-1 rounded">admin</span> 密码任意</p>
                <p>普通用户：任意用户名和密码</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 安全提示 */}
        <div className="mt-6 text-center">
          <p className="text-xs text-muted-foreground">
            您的登录信息将被安全加密传输，符合医疗数据保护标准
          </p>
        </div>
      </div>
    </div>
  );
}